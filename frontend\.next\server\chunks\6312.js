"use strict";exports.id=6312,exports.ids=[6312],exports.modules={1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},9510:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(43210),o=n(11273),i=n(98599),a=n(8730),l=n(60687);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,a.TL)(f),m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(f,n),a=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(p,{ref:a,children:r})});m.displayName=f;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,a.TL)(h),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,u=r.useRef(null),c=(0,i.s)(t,u),d=s(h,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...a}),()=>void d.itemMap.delete(u))),(0,l.jsx)(g,{...{[v]:""},ref:c,children:o})});return y.displayName=h,[{Provider:d,Slot:m,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}var c=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=d(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),o=n(51215),i=n(14163),a=n(66156),l=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let d=n||c&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(i.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},26312:(e,t,n)=>{n.d(t,{UC:()=>eV,q7:()=>eq,JU:()=>eY,ZL:()=>eU,bL:()=>e$,wv:()=>eZ,l9:()=>eX});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(65551),u=n(14163),c=n(9510),s=n(43),d=n(31355),f=n(1359),p=n(32547),m=n(96963),h=n(55509),v=n(25028),g=n(46059),y=n(72942),w=n(8730),b=n(13495),x=n(63376),E=n(42247),R=n(60687),C=["Enter"," "],S=["ArrowUp","PageDown","End"],A=["ArrowDown","PageUp","Home",...S],M={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},L={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[D,P,k]=(0,c.N)(T),[j,O]=(0,a.A)(T,[k,h.Bk,y.RG]),N=(0,h.Bk)(),I=(0,y.RG)(),[F,_]=j(T),[W,B]=j(T),K=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=N(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,b.c)(a),m=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,R.jsx)(h.bL,{...u,children:(0,R.jsx)(F,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,R.jsx)(W,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};K.displayName=T;var G=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,R.jsx)(h.Mz,{...o,...r,ref:t})});G.displayName="MenuAnchor";var H="MenuPortal",[z,$]=j(H,{forceMount:void 0}),X=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=_(H,t);return(0,R.jsx)(z,{scope:t,forceMount:n,children:(0,R.jsx)(g.C,{present:n||i.open,children:(0,R.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};X.displayName=H;var U="MenuContent",[V,Y]=j(U),q=r.forwardRef((e,t)=>{let n=$(U,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=_(U,e.__scopeMenu),a=B(U,e.__scopeMenu);return(0,R.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(g.C,{present:r||i.open,children:(0,R.jsx)(D.Slot,{scope:e.__scopeMenu,children:a.modal?(0,R.jsx)(Z,{...o,ref:t}):(0,R.jsx)(J,{...o,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=_(U,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,R.jsx)(ee,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=_(U,e.__scopeMenu);return(0,R.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,w.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:C,...M}=e,L=_(U,n),T=B(U,n),D=N(n),k=I(n),j=P(n),[O,F]=r.useState(null),W=r.useRef(null),K=(0,i.s)(t,W,L.onContentChange),G=r.useRef(0),H=r.useRef(""),z=r.useRef(0),$=r.useRef(null),X=r.useRef("right"),Y=r.useRef(0),q=C?E.A:r.Fragment,Z=e=>{let t=H.current+e,n=j().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){H.current=t,window.clearTimeout(G.current),""!==t&&(G.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(G.current),[]),(0,f.Oh)();let J=r.useCallback(e=>X.current===$.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,$.current?.area),[]);return(0,R.jsx)(V,{scope:n,searchRef:H,onItemEnter:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:r.useCallback(e=>{J(e)||(W.current?.focus(),F(null))},[J]),onTriggerLeave:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:z,onPointerGraceIntentChange:r.useCallback(e=>{$.current=e},[]),children:(0,R.jsx)(q,{...C?{as:Q,allowPinchZoom:!0}:void 0,children:(0,R.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),W.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,R.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,R.jsx)(y.bL,{asChild:!0,...k,dir:T.dir,orientation:"vertical",loop:a,currentTabStopId:O,onCurrentTabStopIdChange:F,onEntryFocus:(0,o.m)(m,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,R.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eA(L.open),"data-radix-menu-content":"",dir:T.dir,...D,...M,ref:K,style:{outline:"none",...M.style},onKeyDown:(0,o.m)(M.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Z(e.key));let o=W.current;if(e.target!==o||!A.includes(e.key))return;e.preventDefault();let i=j().filter(e=>!e.disabled).map(e=>e.ref.current);S.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(G.current),H.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(X.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});q.displayName=U;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,R.jsx)(u.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,R.jsx)(u.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ei=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),s=B(er,e.__scopeMenu),d=Y(er,e.__scopeMenu),f=(0,i.s)(t,c),p=r.useRef(!1);return(0,R.jsx)(ea,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>a?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=er;var ea=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,s=Y(er,n),d=I(n),f=r.useRef(null),p=(0,i.s)(t,f),[m,h]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[c.children]),(0,R.jsx)(D.ItemSlot,{scope:n,disabled:a,textValue:l??v,children:(0,R.jsx)(y.q7,{asChild:!0,...d,focusable:!a,children:(0,R.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),el=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,R.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,R.jsx)(ei,{role:"menuitemcheckbox","aria-checked":eM(n)?"mixed":n,...i,ref:t,"data-state":eL(n),onSelect:(0,o.m)(i.onSelect,()=>r?.(!!eM(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,es]=j(eu,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,b.c)(r);return(0,R.jsx)(ec,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,R.jsx)(et,{...o,ref:t})})});ed.displayName=eu;var ef="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=es(ef,e.__scopeMenu),a=n===i.value;return(0,R.jsx)(eh,{scope:e.__scopeMenu,checked:a,children:(0,R.jsx)(ei,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eL(a),onSelect:(0,o.m)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var em="MenuItemIndicator",[eh,ev]=j(em,{checked:!1}),eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=ev(em,n);return(0,R.jsx)(g.C,{present:r||eM(i.checked)||!0===i.checked,children:(0,R.jsx)(u.sG.span,{...o,ref:t,"data-state":eL(i.checked)})})});eg.displayName=em;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,R.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,R.jsx)(h.i3,{...o,...r,ref:t})});ew.displayName="MenuArrow";var[eb,ex]=j("MenuSub"),eE="MenuSubTrigger",eR=r.forwardRef((e,t)=>{let n=_(eE,e.__scopeMenu),a=B(eE,e.__scopeMenu),l=ex(eE,e.__scopeMenu),u=Y(eE,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,R.jsx)(G,{asChild:!0,...f,children:(0,R.jsx)(ea,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eA(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eT(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;e.disabled||r&&" "===t.key||M[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eR.displayName=eE;var eC="MenuSubContent",eS=r.forwardRef((e,t)=>{let n=$(U,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=_(U,e.__scopeMenu),c=B(U,e.__scopeMenu),s=ex(eC,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,R.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(g.C,{present:a||u.open,children:(0,R.jsx)(D.Slot,{scope:e.__scopeMenu,children:(0,R.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=L[c.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eA(e){return e?"open":"closed"}function eM(e){return"indeterminate"===e}function eL(e){return eM(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=eC;var eD="DropdownMenu",[eP,ek]=(0,a.A)(eD,[O]),ej=O(),[eO,eN]=eP(eD),eI=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=ej(t),d=r.useRef(null),[f,p]=(0,l.i)({prop:i,defaultProp:a??!1,onChange:u,caller:eD});return(0,R.jsx)(eO,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,R.jsx)(K,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eI.displayName=eD;var eF="DropdownMenuTrigger",e_=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eN(eF,n),c=ej(n);return(0,R.jsx)(G,{asChild:!0,...c,children:(0,R.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e_.displayName=eF;var eW=e=>{let{__scopeDropdownMenu:t,...n}=e,r=ej(t);return(0,R.jsx)(X,{...r,...n})};eW.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eN(eB,n),l=ej(n),u=r.useRef(!1);return(0,R.jsx)(q,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eK.displayName=eB,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(et,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(en,{...o,...r,ref:t})});eG.displayName="DropdownMenuLabel";var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(ei,{...o,...r,ref:t})});eH.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(el,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(ed,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(ep,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(eg,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(ey,{...o,...r,ref:t})});ez.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(eR,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=ej(n);return(0,R.jsx)(eS,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e$=eI,eX=e_,eU=eW,eV=eK,eY=eG,eq=eH,eZ=ez},31355:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(43210),i=n(70569),a=n(14163),l=n(98599),u=n(13495),c=n(60687),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(d),[x,E]=o.useState(null),R=x?.ownerDocument??globalThis?.document,[,C]=o.useState({}),S=(0,l.s)(t,e=>E(e)),A=Array.from(b.layers),[M]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),L=A.indexOf(M),T=x?A.indexOf(x):-1,D=b.layersWithOutsidePointerEventsDisabled.size>0,P=T>=L,k=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));P&&!n&&(h?.(e),g?.(e),e.defaultPrevented||y?.())},R),j=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...b.branches].some(e=>e.contains(t))&&(v?.(e),g?.(e),e.defaultPrevented||y?.())},R);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T===b.layers.size-1&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},R),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[x,R,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...w,ref:S,style:{pointerEvents:D?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),i=n(14163),a=n(13495),l=n(60687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),R=r.useRef(null),C=(0,o.s)(t,e=>b(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(S.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:m(R.current,{select:!0})},t=function(e){if(S.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||m(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,S.paused]),r.useEffect(()=>{if(w){h.add(S);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(c,E),h.remove(S)},0)}}},[w,x,E,S]);let A=r.useCallback(e=>{if(!n&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,S.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},42247:(e,t,n)=>{n.d(t,{A:()=>X});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(43210)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var m=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),h=function(){},v=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,C=e.sideCar,S=e.noRelative,A=e.noIsolation,M=e.inert,L=e.allowPinchZoom,T=e.as,D=e.gapMode,P=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),j=i(i({},P),v);return l.createElement(l.Fragment,null,E&&l.createElement(C,{sideCar:m,removeScrollBar:x,shards:R,noRelative:S,noIsolation:A,inert:M,setCallbacks:g,allowPinchZoom:!!L,lockRef:c,gapMode:D}),y?l.cloneElement(l.Children.only(w),i(i({},j),{ref:k})):l.createElement(void 0===T?"div":T,i({},j,{className:b,ref:k}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=b(),A="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},L=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},T=function(){l.useEffect(function(){return document.body.setAttribute(A,(L()+1).toString()),function(){var e=L()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},D=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=l.useMemo(function(){return C(o)},[o]);return l.createElement(S,{styles:M(i,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var k=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",k,k),window.removeEventListener("test",k,k)}catch(e){P=!1}var j=!!P&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},N=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var m=F(e,u),h=m[0],v=m[1]-m[2]-a*h;(h||v)&&I(e,u)&&(f+=v,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},K=function(e){return e&&"current"in e?e.current:e},G=0,H=[];let z=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(G++)[0],i=l.useState(b)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(K),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=N(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=N(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(H.length&&H[H.length-1]===i){var n="deltaY"in e?B(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(K).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return H.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){H=H.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var m=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(D,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(r),g);var $=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:z}))});$.classNames=v.classNames;let X=$},55509:(e,t,n)=>{n.d(t,{Mz:()=>te,i3:()=>tn,UC:()=>tt,bL:()=>e8,Bk:()=>eU});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let b=["left","right"],x=["right","left"],E=["top","bottom"],R=["bottom","top"];function C(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function A(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function M(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=h(y(t)),u=v(l),c=p(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[l]-=g*(n&&s?-1:1);break;case"end":r[l]+=g*(n&&s?-1:1)}return r}let L=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=M(c,r,u),f=r,p={},m=0;for(let n=0;n<l.length;n++){let{name:i,fn:h}=l[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=M(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function T(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=S(m),v=l[p?"floating"===d?"reference":"floating":d],g=A(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),b=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=A(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-x.top+h.top)/b.y,bottom:(x.bottom-g.bottom+h.bottom)/b.y,left:(g.left-x.left+h.left)/b.x,right:(x.right-g.right+h.right)/b.x}}function D(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return o.some(t=>e[t]>=0)}let k=new Set(["left","top"]);async function j(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=m(n),u="y"===y(n),c=k.has(a)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),u?{x:v*s,y:h*c}:{x:h*c,y:v*s}}function O(){return"undefined"!=typeof window}function N(e){return _(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function F(e){var t;return null==(t=(_(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function _(e){return!!O()&&(e instanceof Node||e instanceof I(e).Node)}function W(e){return!!O()&&(e instanceof Element||e instanceof I(e).Element)}function B(e){return!!O()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function K(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let G=new Set(["inline","contents"]);function H(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!G.has(o)}let z=new Set(["table","td","th"]),$=[":popover-open",":modal"];function X(e){return $.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let U=["transform","translate","scale","rotate","perspective"],V=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function q(e){let t=Z(),n=W(e)?ee(e):e;return U.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||V.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(N(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||K(e)&&e.host||F(e);return K(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:B(n)&&H(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=I(o);if(i){let e=eo(a);return t.concat(a,a.visualViewport||[],H(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=B(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function ea(e){return W(e)?e:e.contextElement}function el(e){let t=ea(e);if(!B(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let eu=c(0);function ec(e){let t=I(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ea(e),l=c(1);t&&(r?W(r)&&(l=el(r)):l=el(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===I(a))&&o)?ec(a):c(0),s=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=I(a),t=r&&W(r)?I(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=a,o=eo(n=I(o))}}return A({width:f,height:p,x:s,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:es(F(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function em(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=I(e),r=F(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=Z();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=F(e),n=et(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ed(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(F(e));else if(W(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=B(e)?el(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return A(r)}function eh(e){return"static"===ee(e).position}function ev(e,t){if(!B(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return F(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=I(e);if(X(e))return r;if(!B(e)){let t=en(e);for(;t&&!Q(t);){if(W(t)&&!eh(t))return t;t=en(t)}return r}let o=ev(e,t);for(;o&&(n=o,z.has(N(n)))&&eh(o);)o=ev(o,t);return o&&Q(o)&&eh(o)&&!q(o)?r:o||function(e){let t=en(e);for(;B(t)&&!Q(t);){if(q(t))return t;if(X(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=B(t),o=F(t),i="fixed"===n,a=es(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==N(t)||H(o))&&(l=et(t)),r){let e=es(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o));i&&!r&&o&&(u.x=ed(o));let s=!o||r||i?c(0):ef(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=F(r),l=!!t&&X(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=B(r);if((f||!f&&!i)&&(("body"!==N(r)||H(a))&&(u=et(r)),B(r))){let e=es(r);s=el(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?c(0):ef(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:F,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?X(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>W(e)&&"body"!==N(e)),o=null,i="fixed"===ee(e).position,a=i?en(e):e;for(;W(a)&&!Q(a);){let t=ee(a),n=q(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||H(a)&&!n&&function e(t,n){let r=en(t);return!(r===n||!W(r)||Q(r))&&("fixed"===ee(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=en(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=em(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},em(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:el,isElement:W,isRTL:function(e){return"rtl"===ee(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=S(p),w={x:n,y:r},b=h(y(o)),x=v(b),E=await u.getDimensions(d),R="y"===b,C=R?"clientHeight":"clientWidth",A=l.reference[x]+l.reference[b]-w[b]-l.floating[x],M=w[b]-l.reference[b],L=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),T=L?L[C]:0;T&&await (null==u.isElement?void 0:u.isElement(L))||(T=c.floating[C]||l.floating[x]);let D=T/2-E[x]/2-1,P=i(g[R?"top":"left"],D),k=i(g[R?"bottom":"right"],D),j=T-E[x]-k,O=T/2-E[x]/2+(A/2-M/2),N=a(P,i(O,j)),I=!s.arrow&&null!=m(o)&&O!==N&&l.reference[x]/2-(O<P?P:k)-E[x]/2<0,F=I?O<P?O-P:O-j:0;return{[b]:w[b]+F,data:{[b]:N,centerOffset:O-N-F,...I&&{alignmentOffset:F}},reset:I}}}),eE=(e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return L(e,t,{...o,platform:i})};var eR=n(51215),eC="undefined"!=typeof document?r.useLayoutEffect:function(){};function eS(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eS(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eS(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eA(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eM(e,t){let n=eA(e);return Math.round(t*n)/n}function eL(e){let t=r.useRef(e);return eC(()=>{t.current=e}),t}let eT=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ex({element:n.current,padding:r}).fn(t):{}:n?ex({element:n,padding:r}).fn(t):{}}}),eD=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await j(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},m=await T(t,s),v=y(p(o)),g=h(v),w=d[g],b=d[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+m[e],r=w-m[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=a(n,i(b,r))}let x=c.fn({...t,[g]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:l,[v]:u}}}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=y(o),m=h(d),v=s[m],g=s[d],w=f(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===m?"width":"height",t=k.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[m]:v,[d]:g}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:g}=t,{mainAxis:S=!0,crossAxis:A=!0,fallbackPlacements:M,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:D="none",flipAlignment:P=!0,...k}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let j=p(l),O=y(s),N=p(s)===s,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),F=M||(N||!P?[C(s)]:function(e){let t=C(e);return[w(e),t,w(t)]}(s)),_="none"!==D;!M&&_&&F.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?x:b;return t?b:x;case"left":case"right":return t?E:R;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(s,P,D,I));let W=[s,...F],B=await T(t,k),K=[],G=(null==(r=u.flip)?void 0:r.overflows)||[];if(S&&K.push(B[j]),A){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(y(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=C(a)),[a,C(a)]}(l,c,I);K.push(B[e[0]],B[e[1]])}if(G=[...G,{placement:l,overflows:K}],!K.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=W[e];if(t&&("alignment"!==A||O===y(t)||G.every(e=>e.overflows[0]>0&&y(e.placement)===O)))return{data:{index:e,overflows:G},reset:{placement:t}};let n=null==(i=G.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(L){case"bestFit":{let e=null==(a=G.filter(e=>{if(_){let t=y(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:c,platform:s,elements:d}=t,{apply:h=()=>{},...v}=f(e,t),g=await T(t,v),w=p(u),b=m(u),x="y"===y(u),{width:E,height:R}=c.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let C=R-g.top-g.bottom,S=E-g.left-g.right,A=i(R-g[o],C),M=i(E-g[l],S),L=!t.middlewareData.shift,D=A,P=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(D=C),L&&!b){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);x?P=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):D=R-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await h({...t,availableWidth:P,availableHeight:D});let k=await s.getDimensions(d.floating);return E!==k.width||R!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=D(await T(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=D(await T(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}}(e),options:[e,t]}),eI=(e,t)=>({...eT(e),options:[e,t]});var eF=n(14163),e_=n(60687),eW=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,e_.jsx)(eF.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,e_.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eW.displayName="Arrow";var eB=n(98599),eK=n(11273),eG=n(13495),eH=n(66156),ez=n(18853),e$="Popper",[eX,eU]=(0,eK.A)(e$),[eV,eY]=eX(e$),eq=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,e_.jsx)(eV,{scope:t,anchor:o,onAnchorChange:i,children:n})};eq.displayName=e$;var eZ="PopperAnchor",eJ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eY(eZ,n),l=r.useRef(null),u=(0,eB.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,e_.jsx)(eF.sG.div,{...i,ref:u})});eJ.displayName=eZ;var eQ="PopperContent",[e0,e1]=eX(eQ),e5=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:c="center",alignOffset:s=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eY(eQ,n),[x,E]=r.useState(null),R=(0,eB.s)(t,e=>E(e)),[C,S]=r.useState(null),A=(0,ez.X)(C),M=A?.width??0,L=A?.height??0,T="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},D=Array.isArray(p)?p:[p],P=D.length>0,k={padding:T,boundary:D.filter(e9),altBoundary:P},{refs:j,floatingStyles:O,placement:N,isPositioned:I,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);eS(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=a||h,E=l||g,R=r.useRef(null),C=r.useRef(null),S=r.useRef(d),A=null!=c,M=eL(c),L=eL(i),T=eL(s),D=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),eE(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};P.current&&!eS(S.current,t)&&(S.current=t,eR.flushSync(()=>{f(t)}))})},[p,t,n,L,T]);eC(()=>{!1===s&&S.current.isPositioned&&(S.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let P=r.useRef(!1);eC(()=>(P.current=!0,()=>{P.current=!1}),[]),eC(()=>{if(x&&(R.current=x),E&&(C.current=E),x&&E){if(M.current)return M.current(x,E,D);D()}},[x,E,D,M,A]);let k=r.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:b}),[w,b]),j=r.useMemo(()=>({reference:x,floating:E}),[x,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eM(j.floating,d.x),r=eM(j.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eA(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:D,refs:k,elements:j,floatingStyles:O}),[d,D,k,j,O])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=ea(e),m=l||c?[...p?er(p):[],...er(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&d?function(e,t){let n,r=null,o=F(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(s||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||eb(f,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?es(e):null;return f&&function t(){let r=es(e);y&&!eb(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[eD({mainAxis:l+L,alignmentAxis:s}),f&&eP({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ek():void 0,...k}),f&&ej({...k}),eO({...k,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&eI({element:C,padding:d}),e4({arrowWidth:M,arrowHeight:L}),v&&eN({strategy:"referenceHidden",...k})]}),[W,B]=e7(N),K=(0,eG.c)(y);(0,eH.N)(()=>{I&&K?.()},[I,K]);let G=_.arrow?.x,H=_.arrow?.y,z=_.arrow?.centerOffset!==0,[$,X]=r.useState();return(0,eH.N)(()=>{x&&X(window.getComputedStyle(x).zIndex)},[x]),(0,e_.jsx)("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:I?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:$,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,e_.jsx)(e0,{scope:n,placedSide:W,onArrowChange:S,arrowX:G,arrowY:H,shouldHideArrow:z,children:(0,e_.jsx)(eF.sG.div,{"data-side":W,"data-align":B,...w,ref:R,style:{...w.style,animation:I?void 0:"none"}})})})});e5.displayName=eQ;var e2="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e6=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e1(e2,n),i=e3[o.placedSide];return(0,e_.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,e_.jsx)(eW,{...r,ref:t,style:{...r.style,display:"block"}})})});function e9(e){return null!==e}e6.displayName=e2;var e4=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=e7(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===u?(p=i?s:`${d}px`,m=`${-l}px`):"top"===u?(p=i?s:`${d}px`,m=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,m=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,m=i?s:`${f}px`),{data:{x:p,y:m}}}});function e7(e){let[t,n="center"]=e.split("-");return[t,n]}var e8=eq,te=eJ,tt=e5,tn=e6},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},65551:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[c,e,l,u])]}Symbol("RADIX:SYNC_STATE")},72942:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>D,q7:()=>P});var r=n(43210),o=n(70569),i=n(9510),a=n(98599),l=n(11273),u=n(96963),c=n(14163),s=n(13495),d=n(65551),f=n(43),p=n(60687),m="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,i.N)(v),[b,x]=(0,l.A)(v,[w]),[E,R]=b(v),C=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(S,{...e,ref:t})})}));C.displayName=v;var S=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:R=!1,...C}=e,S=r.useRef(null),A=(0,a.s)(t,S),M=(0,f.jH)(u),[L,D]=(0,d.i)({prop:g,defaultProp:w??null,onChange:b,caller:v}),[P,k]=r.useState(!1),j=(0,s.c)(x),O=y(n),N=r.useRef(!1),[I,F]=r.useState(0);return r.useEffect(()=>{let e=S.current;if(e)return e.addEventListener(m,j),()=>e.removeEventListener(m,j)},[j]),(0,p.jsx)(E,{scope:n,orientation:i,dir:M,loop:l,currentTabStopId:L,onItemFocus:r.useCallback(e=>D(e),[D]),onItemShiftTab:r.useCallback(()=>k(!0),[]),onFocusableItemAdd:r.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>F(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:P||0===I?-1:0,"data-orientation":i,...C,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(m,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===L),...e].filter(Boolean).map(e=>e.ref.current),R)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>k(!1))})})}),A="RovingFocusGroupItem",M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:s,...d}=e,f=(0,u.B)(),m=l||f,h=R(A,n),v=h.currentTabStopId===m,w=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=h;return r.useEffect(()=>{if(i)return b(),()=>x()},[i,b,x]),(0,p.jsx)(g.ItemSlot,{scope:n,id:m,focusable:i,active:a,children:(0,p.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?h.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>T(n))}}),children:"function"==typeof s?s({isCurrentTabStop:v,hasTabStop:null!=E}):s})})});M.displayName=A;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var D=C,P=M},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}}};
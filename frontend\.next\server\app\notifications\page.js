(()=>{var e={};e.id=6173,e.ids=[6173],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12834:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,63555)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\notifications\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\notifications\\page.tsx"],o={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19080:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32531:(e,s,t)=>{Promise.resolve().then(t.bind(t,71380))},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63555:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\notifications\\page.tsx","default")},71380:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(60687),r=t(43210),i=t(63213),l=t(46952),n=t(32884),c=t(44493),d=t(29523),x=t(96834),o=t(89667),m=t(97051),h=t(19080),p=t(85778);let u=(0,t(62688).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var j=t(84027),y=t(40945),f=t(43649),N=t(99270),b=t(40228),g=t(25334),v=t(88233);function w(){let{user:e,profile:s}=(0,i.A)(),{notifications:t,unreadCount:w,markAsRead:k,markAllAsRead:A,removeNotification:C,clearAll:_,getNotificationsByType:P,getUnreadNotifications:q}=(0,l.lO)(),[Z,$]=(0,r.useState)(""),[M,G]=(0,r.useState)("all"),[E,R]=(0,r.useState)("all"),W=t.filter(e=>{let s=e.title.toLowerCase().includes(Z.toLowerCase())||e.message.toLowerCase().includes(Z.toLowerCase()),t="all"===M||e.type===M,a="all"===E||e.priority===E;return s&&t&&a}),D={total:t.length,unread:w,orders:P("order_confirmed").length+P("order_shipped").length+P("order_delivered").length,payments:P("payment_received").length+P("payment_failed").length,promotions:P("promotion").length,system:P("system").length},z=[{id:"all",label:"الكل",icon:(0,a.jsx)(m.A,{className:"h-4 w-4"}),count:D.total},{id:"order_confirmed",label:"الطلبات",icon:(0,a.jsx)(h.A,{className:"h-4 w-4"}),count:D.orders},{id:"payment_received",label:"المدفوعات",icon:(0,a.jsx)(p.A,{className:"h-4 w-4"}),count:D.payments},{id:"promotion",label:"العروض",icon:(0,a.jsx)(u,{className:"h-4 w-4"}),count:D.promotions},{id:"system",label:"النظام",icon:(0,a.jsx)(j.A,{className:"h-4 w-4"}),count:D.system}],T=(e,s)=>{k(e),s&&window.open(s,"_blank")};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,a.jsx)(n.V,{}),(0,a.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"الإشعارات \uD83D\uDD14"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تابع جميع إشعاراتك ورسائلك"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[w>0&&(0,a.jsxs)(d.$,{onClick:A,className:"arabic-text",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"قراءة الكل (",w,")"]}),(0,a.jsxs)(d.$,{variant:"outline",className:"arabic-text",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"إعدادات الإشعارات"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8",children:[(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الإشعارات"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:D.total})]}),(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"غير مقروءة"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:D.unread})]}),(0,a.jsx)(f.A,{className:"h-8 w-8 text-red-600"})]})})}),(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إشعارات الطلبات"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:D.orders})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"العروض الترويجية"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:D.promotions})]}),(0,a.jsx)(u,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,a.jsxs)(c.Zp,{className:"mb-6",children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{className:"arabic-text",children:"البحث والفلترة"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(N.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(o.p,{placeholder:"البحث في الإشعارات...",value:Z,onChange:e=>$(e.target.value),className:"pl-10 arabic-text"})]})}),(0,a.jsx)("div",{className:"flex gap-2 flex-wrap",children:z.map(e=>(0,a.jsxs)(d.$,{variant:M===e.id?"default":"outline",size:"sm",onClick:()=>G(e.id),className:"arabic-text",children:[e.icon,(0,a.jsx)("span",{className:"mr-2",children:e.label}),e.count>0&&(0,a.jsx)(x.E,{variant:"secondary",className:"mr-1",children:e.count})]},e.id))})]})})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2 space-y-4",children:0===W.length?(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"text-center py-12",children:[(0,a.jsx)(m.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text",children:"لا توجد إشعارات"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:Z?"لم يتم العثور على إشعارات تطابق البحث":"ستظهر إشعاراتك هنا"})]})}):W.map(e=>(0,a.jsx)(c.Zp,{className:`cursor-pointer transition-all hover:shadow-md ${!e.isRead?"ring-2 ring-blue-200 bg-blue-50/30 dark:bg-blue-900/10":""}`,onClick:()=>T(e.id,e.actionUrl),children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center text-lg ${(0,l.bQ)(e.priority)}`,children:(0,l.By)(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)("h3",{className:`font-semibold arabic-text ${!e.isRead?"text-gray-900 dark:text-white":"text-gray-700 dark:text-gray-300"}`,children:e.title}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[!e.isRead&&(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full"}),(0,a.jsx)(x.E,{variant:"outline",className:(0,l.bQ)(e.priority),children:"urgent"===e.priority?"عاجل":"high"===e.priority?"عالي":"medium"===e.priority?"متوسط":"منخفض"})]})]}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-3 arabic-text",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,l.E$)(e.createdAt)]}),e.metadata?.orderId&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),e.metadata.orderId]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.actionText&&e.actionUrl&&(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:s=>{s.stopPropagation(),T(e.id,e.actionUrl)},className:"arabic-text",children:[e.actionText,(0,a.jsx)(g.A,{className:"h-3 w-3 mr-1"})]}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:s=>{s.stopPropagation(),C(e.id)},className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),e.expiresAt&&(0,a.jsx)("div",{className:"mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800 dark:text-yellow-200 arabic-text",children:["⏰ ينتهي في ",(0,l.E$)(e.expiresAt)]})})]})]})})},e.id))}),(0,a.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{className:"arabic-text",children:"إجراءات سريعة"})}),(0,a.jsxs)(c.Wu,{className:"space-y-3",children:[(0,a.jsxs)(d.$,{variant:"outline",className:"w-full arabic-text",onClick:A,disabled:0===w,children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"قراءة جميع الإشعارات"]}),(0,a.jsxs)(d.$,{variant:"outline",className:"w-full arabic-text",onClick:_,disabled:0===t.length,children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"حذف جميع الإشعارات"]}),(0,a.jsxs)(d.$,{variant:"outline",className:"w-full arabic-text",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"إعدادات الإشعارات"]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{className:"arabic-text",children:"النشاط الأخير"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[q().slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded",children:[(0,a.jsx)("div",{className:"text-sm",children:(0,l.By)(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate arabic-text",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:(0,l.E$)(e.createdAt)})]})]},e.id)),0===q().length&&(0,a.jsx)("p",{className:"text-sm text-gray-500 text-center arabic-text",children:"لا توجد إشعارات جديدة"})]})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{className:"arabic-text",children:"إعدادات الإشعارات"})}),(0,a.jsxs)(c.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm arabic-text",children:"إشعارات الطلبات"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm arabic-text",children:"العروض الترويجية"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm arabic-text",children:"إشعارات النظام"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm arabic-text",children:"إشعارات البريد الإلكتروني"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]})]})]})]})]})]})]})}},79387:(e,s,t)=>{Promise.resolve().then(t.bind(t,63555))},79551:e=>{"use strict";e.exports=require("url")},85778:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[7719,3406,1658,5814,6312,5346,4717,8995,2884],()=>t(12834));module.exports=a})();
(()=>{var e={};e.id=3654,e.ids=[3654],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75418:()=>{throw Error("Module parse failed: Identifier 'POST' has already been declared (112:22)\nFile was processed with these loaders:\n * ./node_modules/next/dist/build/webpack/loaders/next-flight-loader/index.js\n * ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js\nYou may need an additional loader to handle the result of these loaders.\n| }\n| // POST - إضافة صفحة جديدة\n> export async function POST(request) {\n|     try {\n|         const cookieStore = cookies();")},78335:()=>{},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)},99850:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>i,serverHooks:()=>u,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var s=t(96559),o=t(48088),a=t(37719),n=t(75418);let i=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/pages/route",pathname:"/api/pages",filename:"route",bundlePath:"app/api/pages/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:u}=i;function l(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719],()=>t(99850));module.exports=s})();
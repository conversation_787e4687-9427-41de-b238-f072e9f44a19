{"version": 3, "file": "sortable.cjs.production.min.js", "sources": ["../src/utilities/arrayMove.ts", "../src/utilities/getSortedRects.ts", "../src/utilities/isValidIndex.ts", "../src/strategies/horizontalListSorting.ts", "../src/strategies/rectSorting.ts", "../src/strategies/verticalListSorting.ts", "../src/components/SortableContext.tsx", "../src/hooks/defaults.ts", "../src/types/type-guard.ts", "../src/sensors/keyboard/sortableKeyboardCoordinates.ts", "../src/utilities/itemsEqual.ts", "../src/utilities/normalizeDisabled.ts", "../src/utilities/arraySwap.ts", "../src/strategies/rectSwapping.ts", "../src/hooks/useSortable.ts", "../src/hooks/utilities/useDerivedTransform.ts"], "sourcesContent": ["/**\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\n */\nexport function arrayMove<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n  newArray.splice(\n    to < 0 ? newArray.length + to : to,\n    0,\n    newArray.splice(from, 1)[0]\n  );\n\n  return newArray;\n}\n", "import type {\n  ClientRect,\n  UniqueIdentifier,\n  UseDndContextReturnValue,\n} from '@dnd-kit/core';\n\nexport function getSortedRects(\n  items: UniqueIdentifier[],\n  rects: UseDndContextReturnValue['droppableRects']\n) {\n  return items.reduce<ClientRect[]>((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n", "export function isValidIndex(index: number | null): index is number {\n  return index !== null && index >= 0;\n}\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const horizontalListSortingStrategy: SortingStrategy = ({\n  rects,\n  activeNodeRect: fallbackActiveRect,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x:\n        activeIndex < overIndex\n          ? newIndexRect.left +\n            newIndexRect.width -\n            (activeNodeRect.left + activeNodeRect.width)\n          : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(rects: ClientRect[], index: number, activeIndex: number) {\n  const currentRect: ClientRect | undefined = rects[index];\n  const previousRect: ClientRect | undefined = rects[index - 1];\n  const nextRect: ClientRect | undefined = rects[index + 1];\n\n  if (!currentRect || (!previousRect && !nextRect)) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.left - (previousRect.left + previousRect.width)\n      : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect\n    ? nextRect.left - (currentRect.left + currentRect.width)\n    : currentRect.left - (previousRect.left + previousRect.width);\n}\n", "import {arrayMove} from '../utilities';\nimport type {SortingStrategy} from '../types';\n\nexport const rectSortingStrategy: SortingStrategy = ({\n  rects,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const verticalListSortingStrategy: SortingStrategy = ({\n  activeIndex,\n  activeNodeRect: fallbackActiveRect,\n  index,\n  rects,\n  overIndex,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y:\n        activeIndex < overIndex\n          ? overIndexRect.top +\n            overIndexRect.height -\n            (activeNodeRect.top + activeNodeRect.height)\n          : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale,\n    };\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(\n  clientRects: ClientRect[],\n  index: number,\n  activeIndex: number\n) {\n  const currentRect: ClientRect | undefined = clientRects[index];\n  const previousRect: ClientRect | undefined = clientRects[index - 1];\n  const nextRect: ClientRect | undefined = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.top - (previousRect.top + previousRect.height)\n      : nextRect\n      ? nextRect.top - (currentRect.top + currentRect.height)\n      : 0;\n  }\n\n  return nextRect\n    ? nextRect.top - (currentRect.top + currentRect.height)\n    : previousRect\n    ? currentRect.top - (previousRect.top + previousRect.height)\n    : 0;\n}\n", "import React, {useEffect, useMemo, useRef} from 'react';\nimport {useDndContext, ClientRect, UniqueIdentifier} from '@dnd-kit/core';\nimport {useIsomorphicLayoutEffect, useUniqueId} from '@dnd-kit/utilities';\n\nimport type {Disabled, SortingStrategy} from '../types';\nimport {getSortedRects, itemsEqual, normalizeDisabled} from '../utilities';\nimport {rectSortingStrategy} from '../strategies';\n\nexport interface Props {\n  children: React.ReactNode;\n  items: (UniqueIdentifier | {id: UniqueIdentifier})[];\n  strategy?: SortingStrategy;\n  id?: string;\n  disabled?: boolean | Disabled;\n}\n\nconst ID_PREFIX = 'Sortable';\n\ninterface ContextDescriptor {\n  activeIndex: number;\n  containerId: string;\n  disabled: Disabled;\n  disableTransforms: boolean;\n  items: UniqueIdentifier[];\n  overIndex: number;\n  useDragOverlay: boolean;\n  sortedRects: ClientRect[];\n  strategy: SortingStrategy;\n}\n\nexport const Context = React.createContext<ContextDescriptor>({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false,\n  },\n});\n\nexport function SortableContext({\n  children,\n  id,\n  items: userDefinedItems,\n  strategy = rectSortingStrategy,\n  disabled: disabledProp = false,\n}: Props) {\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo<UniqueIdentifier[]>(\n    () =>\n      userDefinedItems.map((item) =>\n        typeof item === 'object' && 'id' in item ? item.id : item\n      ),\n    [userDefinedItems]\n  );\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms =\n    (overIndex !== -1 && activeIndex === -1) || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n\n  const contextValue = useMemo(\n    (): ContextDescriptor => ({\n      activeIndex,\n      containerId,\n      disabled,\n      disableTransforms,\n      items,\n      overIndex,\n      useDragOverlay,\n      sortedRects: getSortedRects(items, droppableRects),\n      strategy,\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      activeIndex,\n      containerId,\n      disabled.draggable,\n      disabled.droppable,\n      disableTransforms,\n      items,\n      overIndex,\n      droppableRects,\n      useDragOverlay,\n      strategy,\n    ]\n  );\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>;\n}\n", "import {CSS} from '@dnd-kit/utilities';\n\nimport {arrayMove} from '../utilities';\n\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\n\nexport const defaultNewIndexGetter: NewIndexGetter = ({\n  id,\n  items,\n  activeIndex,\n  overIndex,\n}) => arrayMove(items, activeIndex, overIndex).indexOf(id);\n\nexport const defaultAnimateLayoutChanges: AnimateLayoutChanges = ({\n  containerId,\n  isSorting,\n  wasDragging,\n  index,\n  items,\n  newIndex,\n  previousItems,\n  previousContainerId,\n  transition,\n}) => {\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\n\nexport const defaultTransition: SortableTransition = {\n  duration: 200,\n  easing: 'ease',\n};\n\nexport const transitionProperty = 'transform';\n\nexport const disabledTransition = CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear',\n});\n\nexport const defaultAttributes = {\n  roleDescription: 'sortable',\n};\n", "import type {\n  Active,\n  Data,\n  DroppableContainer,\n  DraggableNode,\n  Over,\n} from '@dnd-kit/core';\n\nimport type {SortableData} from './data';\n\nexport function hasSortableData<\n  T extends Active | Over | DraggableNode | DroppableContainer\n>(\n  entry: T | null | undefined\n): entry is T & {data: {current: Data<SortableData>}} {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (\n    data &&\n    'sortable' in data &&\n    typeof data.sortable === 'object' &&\n    'containerId' in data.sortable &&\n    'items' in data.sortable &&\n    'index' in data.sortable\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "import {\n  closestCorners,\n  getScrollableAncestors,\n  getFirstCollision,\n  KeyboardCode,\n  DroppableContainer,\n  KeyboardCoordinateGetter,\n} from '@dnd-kit/core';\nimport {subtract} from '@dnd-kit/utilities';\n\nimport {hasSortableData} from '../../types';\n\nconst directions: string[] = [\n  KeyboardCode.Down,\n  KeyboardCode.Right,\n  KeyboardCode.Up,\n  KeyboardCode.Left,\n];\n\nexport const sortableKeyboardCoordinates: KeyboardCoordinateGetter = (\n  event,\n  {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n    },\n  }\n) => {\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers: DroppableContainer[] = [];\n\n    droppableContainers.getEnabled().forEach((entry) => {\n      if (!entry || entry?.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n      }\n    });\n\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null,\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n\n    if (closestId === over?.id && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable?.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some(\n          (element, index) => scrollableAncestors[index] !== element\n        );\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset =\n          hasDifferentScrollAncestors || !hasSameContainer\n            ? {\n                x: 0,\n                y: 0,\n              }\n            : {\n                x: isAfterActive ? collisionRect.width - newRect.width : 0,\n                y: isAfterActive ? collisionRect.height - newRect.height : 0,\n              };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top,\n        };\n\n        const newCoordinates =\n          offset.x && offset.y\n            ? rectCoordinates\n            : subtract(rectCoordinates, offset);\n\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return (\n    a.data.current.sortable.containerId === b.data.current.sortable.containerId\n  );\n}\n\nfunction isAfter(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n", "import type {UniqueIdentifier} from '@dnd-kit/core';\n\nexport function itemsEqual(a: UniqueIdentifier[], b: UniqueIdentifier[]) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import type {Disabled} from '../types';\n\nexport function normalizeDisabled(disabled: boolean | Disabled): Disabled {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled,\n    };\n  }\n\n  return disabled;\n}\n", "/**\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\n */\nexport function arraySwap<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n\n  return newArray;\n}\n", "import type {SortingStrategy} from '../types';\n\nexport const rectSwappingStrategy: SortingStrategy = ({\n  activeIndex,\n  index,\n  rects,\n  overIndex,\n}) => {\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import {useContext, useEffect, useMemo, useRef} from 'react';\nimport {\n  useDraggable,\n  useDroppable,\n  UseDraggableArguments,\n  UseDroppableArguments,\n} from '@dnd-kit/core';\nimport type {Data} from '@dnd-kit/core';\nimport {CSS, isKeyboardEvent, useCombinedRefs} from '@dnd-kit/utilities';\n\nimport {Context} from '../components';\nimport type {Disabled, SortableData, SortingStrategy} from '../types';\nimport {isValidIndex} from '../utilities';\nimport {\n  defaultAnimateLayoutChanges,\n  defaultAttributes,\n  defaultNewIndexGetter,\n  defaultTransition,\n  disabledTransition,\n  transitionProperty,\n} from './defaults';\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\nimport {useDerivedTransform} from './utilities';\n\nexport interface Arguments\n  extends Omit<UseDraggableArguments, 'disabled'>,\n    Pick<UseDroppableArguments, 'resizeObserverConfig'> {\n  animateLayoutChanges?: AnimateLayoutChanges;\n  disabled?: boolean | Disabled;\n  getNewIndex?: NewIndexGetter;\n  strategy?: SortingStrategy;\n  transition?: SortableTransition | null;\n}\n\nexport function useSortable({\n  animateLayoutChanges = defaultAnimateLayoutChanges,\n  attributes: userDefinedAttributes,\n  disabled: localDisabled,\n  data: customData,\n  getNewIndex = defaultNewIndexGetter,\n  id,\n  strategy: localStrategy,\n  resizeObserverConfig,\n  transition = defaultTransition,\n}: Arguments) {\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy,\n  } = useContext(Context);\n  const disabled: Disabled = normalizeLocalDisabled(\n    localDisabled,\n    globalDisabled\n  );\n  const index = items.indexOf(id);\n  const data = useMemo<SortableData & Data>(\n    () => ({sortable: {containerId, index, items}, ...customData}),\n    [containerId, customData, index, items]\n  );\n  const itemsAfterCurrentSortable = useMemo(\n    () => items.slice(items.indexOf(id)),\n    [items, id]\n  );\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef,\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig,\n    },\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform,\n  } = useDraggable({\n    id,\n    data,\n    attributes: {\n      ...defaultAttributes,\n      ...userDefinedAttributes,\n    },\n    disabled: disabled.draggable,\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem =\n    isSorting &&\n    !disableTransforms &&\n    isValidIndex(activeIndex) &&\n    isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement =\n    shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy ?? globalStrategy;\n  const finalTransform = displaceItem\n    ? dragSourceDisplacement ??\n      strategy({\n        rects: sortedRects,\n        activeNodeRect,\n        activeIndex,\n        overIndex,\n        index,\n      })\n    : null;\n  const newIndex =\n    isValidIndex(activeIndex) && isValidIndex(overIndex)\n      ? getNewIndex({id, items, activeIndex, overIndex})\n      : index;\n  const activeId = active?.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId,\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null,\n  });\n\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect,\n  });\n\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform ?? finalTransform,\n    transition: getTransition(),\n  };\n\n  function getTransition() {\n    if (\n      // Temporarily disable transitions for a single frame to set up derived transforms\n      derivedTransform ||\n      // Or to prevent items jumping to back to their \"new\" position when items change\n      (itemsHaveChanged && previous.current.newIndex === index)\n    ) {\n      return disabledTransition;\n    }\n\n    if (\n      (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent)) ||\n      !transition\n    ) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({\n        ...transition,\n        property: transitionProperty,\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(\n  localDisabled: Arguments['disabled'],\n  globalDisabled: Disabled\n) {\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false,\n    };\n  }\n\n  return {\n    draggable: localDisabled?.draggable ?? globalDisabled.draggable,\n    droppable: localDisabled?.droppable ?? globalDisabled.droppable,\n  };\n}\n", "import {useEffect, useRef, useState} from 'react';\nimport {getClientRect, ClientRect} from '@dnd-kit/core';\nimport {Transform, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  rect: React.MutableRefObject<ClientRect | null>;\n  disabled: boolean;\n  index: number;\n  node: React.MutableRefObject<HTMLElement | null>;\n}\n\n/*\n * When the index of an item changes while sorting,\n * we need to temporarily disable the transforms\n */\nexport function useDerivedTransform({disabled, index, node, rect}: Arguments) {\n  const [derivedTransform, setDerivedtransform] = useState<Transform | null>(\n    null\n  );\n  const previousIndex = useRef(index);\n\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true,\n        });\n\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height,\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n\n  return derivedTransform;\n}\n"], "names": ["arrayMove", "array", "from", "to", "newArray", "slice", "splice", "length", "getSortedRects", "items", "rects", "reduce", "accumulator", "id", "index", "rect", "get", "Array", "isValidIndex", "defaultScale", "scaleX", "scaleY", "rectSortingStrategy", "_ref", "activeIndex", "overIndex", "newRects", "oldRect", "newRect", "x", "left", "y", "top", "width", "height", "Context", "React", "createContext", "containerId", "disableTransforms", "useDragOverlay", "sortedRects", "strategy", "disabled", "draggable", "droppable", "defaultNewIndexGetter", "indexOf", "defaultAnimateLayoutChanges", "_ref2", "isSorting", "wasDragging", "newIndex", "previousItems", "previousContainerId", "transition", "defaultTransition", "duration", "easing", "disabledTransition", "CSS", "Transition", "toString", "property", "defaultAttributes", "roleDescription", "hasSortableData", "entry", "data", "current", "sortable", "directions", "KeyboardCode", "Down", "Right", "Up", "Left", "isSameContainer", "a", "b", "children", "userDefinedItems", "disabledProp", "active", "dragOverlay", "droppableRects", "over", "measureDroppableContainers", "useDndContext", "useUniqueId", "Boolean", "useMemo", "map", "item", "isDragging", "previousItemsRef", "useRef", "itemsHaveChanged", "i", "itemsEqual", "normalizeDisabled", "useIsomorphicLayoutEffect", "useEffect", "contextValue", "Provider", "value", "activeNodeRect", "fallbackActiveRect", "itemGap", "currentRect", "previousRect", "nextRect", "getItemGap", "newIndexRect", "event", "context", "collisionRect", "droppableContainers", "scrollableAncestors", "includes", "code", "preventDefault", "filteredContainers", "getEnabled", "for<PERSON>ach", "push", "collisions", "closestCorners", "pointerCoordinates", "closestId", "getFirstCollision", "activeDroppable", "newDroppable", "newNode", "node", "hasDifferentScrollAncestors", "getScrollableAncestors", "some", "element", "hasSameContainer", "isAfterActive", "offset", "rectCoordinates", "subtract", "animateLayoutChanges", "attributes", "userDefinedAttributes", "localDisabled", "customData", "getNewIndex", "localStrategy", "resizeObserverConfig", "globalDisabled", "globalStrategy", "useContext", "normalizeLocalDisabled", "itemsAfterCurrentSortable", "isOver", "setNodeRef", "setDroppableNodeRef", "useDroppable", "updateMeasurementsFor", "activatorEvent", "setDraggableNodeRef", "listeners", "setActivatorNodeRef", "transform", "useDraggable", "useCombinedRefs", "displaceItem", "shouldDisplaceDragSource", "dragSourceDisplacement", "finalTransform", "activeId", "previous", "shouldAnimateLayoutChanges", "derivedTransform", "setDerivedtransform", "useState", "previousIndex", "initial", "getClientRect", "ignoreTransform", "delta", "useDerivedTransform", "timeoutId", "setTimeout", "clearTimeout", "isKeyboardEvent", "overIndexRect", "clientRects"], "mappings": "wNAGgBA,EAAaC,EAAYC,EAAcC,GACrD,MAAMC,EAAWH,EAAMI,QAOvB,OANAD,EAASE,OACPH,EAAK,EAAIC,EAASG,OAASJ,EAAKA,EAChC,EACAC,EAASE,OAAOJ,EAAM,GAAG,IAGpBE,WCLOI,EACdC,EACAC,GAEA,OAAOD,EAAME,OAAqB,CAACC,EAAaC,EAAIC,KAClD,MAAMC,EAAOL,EAAMM,IAAIH,GAMvB,OAJIE,IACFH,EAAYE,GAASC,GAGhBH,GACNK,MAAMR,EAAMF,kBClBDW,EAAaJ,GAC3B,OAAiB,OAAVA,GAAkBA,GAAS,ECGpC,MAAMK,EAAe,CACnBC,OAAQ,EACRC,OAAQ,GCHGC,EAAuCC,QAACb,MACnDA,EADmDc,YAEnDA,EAFmDC,UAGnDA,EAHmDX,MAInDA,KAEA,MAAMY,EAAW1B,EAAUU,EAAOe,EAAWD,GAEvCG,EAAUjB,EAAMI,GAChBc,EAAUF,EAASZ,GAEzB,OAAKc,GAAYD,EAIV,CACLE,EAAGD,EAAQE,KAAOH,EAAQG,KAC1BC,EAAGH,EAAQI,IAAML,EAAQK,IACzBZ,OAAQQ,EAAQK,MAAQN,EAAQM,MAChCZ,OAAQO,EAAQM,OAASP,EAAQO,QAP1B,MCXLf,EAAe,CACnBC,OAAQ,EACRC,OAAQ,GCwBGc,EAAUC,EAAMC,cAAiC,CAC5Db,aAAc,EACdc,YAhBgB,WAiBhBC,mBAAmB,EACnB9B,MAAO,GACPgB,WAAY,EACZe,gBAAgB,EAChBC,YAAa,GACbC,SAAUpB,EACVqB,SAAU,CACRC,WAAW,EACXC,WAAW,KC/BFC,EAAwCvB,IAAA,IAACV,GACpDA,EADoDJ,MAEpDA,EAFoDe,YAGpDA,EAHoDC,UAIpDA,KAJmD,OAK/CzB,EAAUS,EAAOe,EAAaC,GAAWsB,QAAQlC,IAE1CmC,EAAoDC,QAACX,YAChEA,EADgEY,UAEhEA,EAFgEC,YAGhEA,EAHgErC,MAIhEA,EAJgEL,MAKhEA,EALgE2C,SAMhEA,EANgEC,cAOhEA,EAPgEC,oBAQhEA,EARgEC,WAShEA,KAEA,SAAKA,IAAeJ,GAIhBE,IAAkB5C,GAASK,IAAUsC,IAIrCF,IAIGE,IAAatC,GAASwB,IAAgBgB,KAGlCE,EAAwC,CACnDC,SAAU,IACVC,OAAQ,QAKGC,EAAqBC,MAAIC,WAAWC,SAAS,CACxDC,SAHgC,YAIhCN,SAAU,EACVC,OAAQ,WAGGM,EAAoB,CAC/BC,gBAAiB,qBC/CHC,EAGdC,GAEA,IAAKA,EACH,OAAO,EAGT,MAAMC,EAAOD,EAAMC,KAAKC,QAExB,SACED,GACA,aAAcA,GACW,iBAAlBA,EAAKE,UACZ,gBAAiBF,EAAKE,UACtB,UAAWF,EAAKE,UAChB,UAAWF,EAAKE,gBCfdC,EAAuB,CAC3BC,eAAaC,KACbD,eAAaE,MACbF,eAAaG,GACbH,eAAaI,MAkHf,SAASC,EAAgBC,EAAuBC,GAC9C,SAAKb,EAAgBY,KAAOZ,EAAgBa,KAK1CD,EAAEV,KAAKC,QAAQC,SAAShC,cAAgByC,EAAEX,KAAKC,QAAQC,SAAShC,oDH3FpC0C,SAC9BA,EAD8BnE,GAE9BA,EACAJ,MAAOwE,EAHuBvC,SAI9BA,EAAWpB,EACXqB,SAAUuC,GAAe,KAEzB,MAAMC,OACJA,EADIC,YAEJA,EAFIC,eAGJA,EAHIC,KAIJA,EAJIC,2BAKJA,GACEC,kBACElD,EAAcmD,cA3CJ,WA2C2B5E,GACrC2B,EAAiBkD,QAA6B,OAArBN,EAAYrE,MACrCN,EAAQkF,UACZ,IACEV,EAAiBW,IAAKC,GACJ,iBAATA,GAAqB,OAAQA,EAAOA,EAAKhF,GAAKgF,GAEzD,CAACZ,IAEGa,EAAuB,MAAVX,EACb3D,EAAc2D,EAAS1E,EAAMsC,QAAQoC,EAAOtE,KAAO,EACnDY,EAAY6D,EAAO7E,EAAMsC,QAAQuC,EAAKzE,KAAO,EAC7CkF,EAAmBC,SAAOvF,GAC1BwF,YItEmBnB,EAAuBC,GAChD,GAAID,IAAMC,EACR,OAAO,EAGT,GAAID,EAAEvE,SAAWwE,EAAExE,OACjB,OAAO,EAGT,IAAK,IAAI2F,EAAI,EAAGA,EAAIpB,EAAEvE,OAAQ2F,IAC5B,GAAIpB,EAAEoB,KAAOnB,EAAEmB,GACb,OAAO,EAIX,OAAO,EJuDmBC,CAAW1F,EAAOsF,EAAiB1B,SACvD9B,GACY,IAAfd,IAAqC,IAAjBD,GAAuByE,EACxCtD,WKzE0BA,GAChC,MAAwB,kBAAbA,EACF,CACLC,UAAWD,EACXE,UAAWF,GAIRA,ELiEUyD,CAAkBlB,GAEnCmB,4BAA0B,KACpBJ,GAAoBH,GACtBP,EAA2B9E,IAE5B,CAACwF,EAAkBxF,EAAOqF,EAAYP,IAEzCe,YAAU,KACRP,EAAiB1B,QAAU5D,GAC1B,CAACA,IAEJ,MAAM8F,EAAeZ,UACnB,MACEnE,YAAAA,EACAc,YAAAA,EACAK,SAAAA,EACAJ,kBAAAA,EACA9B,MAAAA,EACAgB,UAAAA,EACAe,eAAAA,EACAC,YAAajC,EAAeC,EAAO4E,GACnC3C,SAAAA,IAGF,CACElB,EACAc,EACAK,EAASC,UACTD,EAASE,UACTN,EACA9B,EACAgB,EACA4D,EACA7C,EACAE,IAIJ,OAAON,gBAACD,EAAQqE,UAASC,MAAOF,GAAevB,0CM/GjD,SAA6B/E,EAAYC,EAAcC,GACrD,MAAMC,EAAWH,EAAMI,QAKvB,OAHAD,EAASF,GAAQD,EAAME,GACvBC,EAASD,GAAMF,EAAMC,GAEdE,yITAqDmB,cAACb,MAC7DA,EACAgG,eAAgBC,EAF6CnF,YAG7DA,EAH6DC,UAI7DA,EAJ6DX,MAK7DA,KAEA,MAAM4F,WAAiBhG,EAAMc,MAAgBmF,EAE7C,IAAKD,EACH,OAAO,KAGT,MAAME,EA4CR,SAAoBlG,EAAqBI,EAAeU,GACtD,MAAMqF,EAAsCnG,EAAMI,GAC5CgG,EAAuCpG,EAAMI,EAAQ,GACrDiG,EAAmCrG,EAAMI,EAAQ,GAEvD,OAAK+F,IAAiBC,GAAiBC,GAInCvF,EAAcV,EACTgG,EACHD,EAAY/E,MAAQgF,EAAahF,KAAOgF,EAAa7E,OACrD8E,EAASjF,MAAQ+E,EAAY/E,KAAO+E,EAAY5E,OAG/C8E,EACHA,EAASjF,MAAQ+E,EAAY/E,KAAO+E,EAAY5E,OAChD4E,EAAY/E,MAAQgF,EAAahF,KAAOgF,EAAa7E,OAXhD,EAlDO+E,CAAWtG,EAAOI,EAAOU,GAEzC,GAAIV,IAAUU,EAAa,CACzB,MAAMyF,EAAevG,EAAMe,GAE3B,OAAKwF,EAIE,CACLpF,EACEL,EAAcC,EACVwF,EAAanF,KACbmF,EAAahF,OACZyE,EAAe5E,KAAO4E,EAAezE,OACtCgF,EAAanF,KAAO4E,EAAe5E,KACzCC,EAAG,KACAZ,GAXI,KAeX,OAAIL,EAAQU,GAAeV,GAASW,EAC3B,CACLI,GAAI6E,EAAezE,MAAQ2E,EAC3B7E,EAAG,KACAZ,GAIHL,EAAQU,GAAeV,GAASW,EAC3B,CACLI,EAAG6E,EAAezE,MAAQ2E,EAC1B7E,EAAG,KACAZ,GAIA,CACLU,EAAG,EACHE,EAAG,KACAZ,+DU5D8CI,QAM/CI,EACAC,GAPgDJ,YACpDA,EADoDV,MAEpDA,EAFoDJ,MAGpDA,EAHoDe,UAIpDA,KAeA,OAVIX,IAAUU,IACZG,EAAUjB,EAAMI,GAChBc,EAAUlB,EAAMe,IAGdX,IAAUW,IACZE,EAAUjB,EAAMI,GAChBc,EAAUlB,EAAMc,IAGbI,GAAYD,EAIV,CACLE,EAAGD,EAAQE,KAAOH,EAAQG,KAC1BC,EAAGH,EAAQI,IAAML,EAAQK,IACzBZ,OAAQQ,EAAQK,MAAQN,EAAQM,MAChCZ,OAAQO,EAAQM,OAASP,EAAQO,QAP1B,0CJH0D,CACnEgF,WAEEC,SAAShC,OACPA,EADOiC,cAEPA,EAFO/B,eAGPA,EAHOgC,oBAIPA,EAJO/B,KAKPA,EALOgC,oBAMPA,MAIJ,GAAI/C,EAAWgD,SAASL,EAAMM,MAAO,CAGnC,GAFAN,EAAMO,kBAEDtC,IAAWiC,EACd,OAGF,MAAMM,EAA2C,GAEjDL,EAAoBM,aAAaC,QAASzD,IACxC,IAAKA,SAASA,GAAAA,EAAOxB,SACnB,OAGF,MAAM5B,EAAOsE,EAAerE,IAAImD,EAAMtD,IAEtC,GAAKE,EAIL,OAAQmG,EAAMM,MACZ,KAAKhD,eAAaC,KACZ2C,EAAcpF,IAAMjB,EAAKiB,KAC3B0F,EAAmBG,KAAK1D,GAE1B,MACF,KAAKK,eAAaG,GACZyC,EAAcpF,IAAMjB,EAAKiB,KAC3B0F,EAAmBG,KAAK1D,GAE1B,MACF,KAAKK,eAAaI,KACZwC,EAActF,KAAOf,EAAKe,MAC5B4F,EAAmBG,KAAK1D,GAE1B,MACF,KAAKK,eAAaE,MACZ0C,EAActF,KAAOf,EAAKe,MAC5B4F,EAAmBG,KAAK1D,MAMhC,MAAM2D,EAAaC,iBAAe,CAChC5C,OAAAA,EACAiC,cAAeA,EACf/B,eAAAA,EACAgC,oBAAqBK,EACrBM,mBAAoB,OAEtB,IAAIC,EAAYC,oBAAkBJ,EAAY,MAM9C,GAJIG,WAAc3C,SAAAA,EAAMzE,KAAMiH,EAAWvH,OAAS,IAChD0H,EAAYH,EAAW,GAAGjH,IAGX,MAAboH,EAAmB,CACrB,MAAME,EAAkBd,EAAoBrG,IAAImE,EAAOtE,IACjDuH,EAAef,EAAoBrG,IAAIiH,GACvCrG,EAAUwG,EAAe/C,EAAerE,IAAIoH,EAAavH,IAAM,KAC/DwH,QAAUD,SAAAA,EAAcE,KAAKjE,QAEnC,GAAIgE,GAAWzG,GAAWuG,GAAmBC,EAAc,CACzD,MACMG,EADqBC,yBAAuBH,GACKI,KACrD,CAACC,EAAS5H,IAAUwG,EAAoBxG,KAAW4H,GAE/CC,EAAmB9D,EAAgBsD,EAAiBC,GACpDQ,GAuC0B7D,EAvCeqD,KAwChDlE,EADUY,EAvCqBqD,KAwCRjE,EAAgBa,OAIvCF,EAAgBC,EAAGC,IAIjBD,EAAEV,KAAKC,QAAQC,SAASxD,MAAQiE,EAAEX,KAAKC,QAAQC,SAASxD,OA/CnD+H,EACJN,IAAgCI,EAC5B,CACE9G,EAAG,EACHE,EAAG,GAEL,CACEF,EAAG+G,EAAgBxB,EAAcnF,MAAQL,EAAQK,MAAQ,EACzDF,EAAG6G,EAAgBxB,EAAclF,OAASN,EAAQM,OAAS,GAE7D4G,EAAkB,CACtBjH,EAAGD,EAAQE,KACXC,EAAGH,EAAQI,KAQb,OAJE6G,EAAOhH,GAAKgH,EAAO9G,EACf+G,EACAC,WAASD,EAAiBD,KAoBxC,IAAiB/D,EAAuBC,uCKtGZiE,qBAC1BA,EAAuBhG,EACvBiG,WAAYC,EACZvG,SAAUwG,EACV/E,KAAMgF,EAJoBC,YAK1BA,EAAcvG,EALYjC,GAM1BA,EACA6B,SAAU4G,EAPgBC,qBAQ1BA,EAR0BhG,WAS1BA,EAAaC,KAEb,MAAM/C,MACJA,EADI6B,YAEJA,EAFId,YAGJA,EACAmB,SAAU6G,EAJNjH,kBAKJA,EALIE,YAMJA,EANIhB,UAOJA,EAPIe,eAQJA,EACAE,SAAU+G,GACRC,aAAWvH,GACTQ,EAyLR,SACEwG,EACAK,WAEA,MAA6B,kBAAlBL,EACF,CACLvG,UAAWuG,EAEXtG,WAAW,GAIR,CACLD,yBAAWuG,SAAAA,EAAevG,aAAa4G,EAAe5G,UACtDC,yBAAWsG,SAAAA,EAAetG,aAAa2G,EAAe3G,WAvM7B8G,CACzBR,EACAK,GAEI1I,EAAQL,EAAMsC,QAAQlC,GACtBuD,EAAOuB,UACX,MAAQrB,SAAU,CAAChC,YAAAA,EAAaxB,MAAAA,EAAOL,MAAAA,MAAW2I,IAClD,CAAC9G,EAAa8G,EAAYtI,EAAOL,IAE7BmJ,EAA4BjE,UAChC,IAAMlF,EAAMJ,MAAMI,EAAMsC,QAAQlC,IAChC,CAACJ,EAAOI,KAEJE,KACJA,EADIuH,KAEJA,EAFIuB,OAGJA,EACAC,WAAYC,GACVC,eAAa,CACfnJ,GAAAA,EACAuD,KAAAA,EACAzB,SAAUA,EAASE,UACnB0G,qBAAsB,CACpBU,sBAAuBL,KACpBL,MAGDpE,OACJA,EADI+E,eAEJA,EAFIxD,eAGJA,EAHIuC,WAIJA,EACAa,WAAYK,EALRC,UAMJA,EANItE,WAOJA,EAPIR,KAQJA,EARI+E,oBASJA,EATIC,UAUJA,GACEC,eAAa,CACf1J,GAAAA,EACAuD,KAAAA,EACA6E,WAAY,IACPjF,KACAkF,GAELvG,SAAUA,EAASC,YAEfkH,EAAaU,kBAAgBT,EAAqBI,GAClDjH,EAAYwC,QAAQP,GACpBsF,EACJvH,IACCX,GACDrB,EAAaM,IACbN,EAAaO,GACTiJ,GAA4BlI,GAAkBsD,EAC9C6E,EACJD,GAA4BD,EAAeH,EAAY,KAEnDM,EAAiBH,QACnBE,EAAAA,SAFarB,EAAAA,EAAiBG,GAGrB,CACP/I,MAAO+B,EACPiE,eAAAA,EACAlF,YAAAA,EACAC,UAAAA,EACAX,MAAAA,IAEF,KACEsC,EACJlC,EAAaM,IAAgBN,EAAaO,GACtC4H,EAAY,CAACxI,GAAAA,EAAIJ,MAAAA,EAAOe,YAAAA,EAAaC,UAAAA,IACrCX,EACA+J,SAAW1F,SAAAA,EAAQtE,GACnBiK,GAAW9E,SAAO,CACtB6E,SAAAA,GACApK,MAAAA,EACA2C,SAAAA,EACAd,YAAAA,IAEI2D,GAAmBxF,IAAUqK,GAASzG,QAAQ5D,MAC9CsK,GAA6B/B,EAAqB,CACtD7D,OAAAA,EACA7C,YAAAA,EACAwD,WAAAA,EACA5C,UAAAA,EACArC,GAAAA,EACAC,MAAAA,EACAL,MAAAA,EACA2C,SAAU0H,GAASzG,QAAQjB,SAC3BC,cAAeyH,GAASzG,QAAQ5D,MAChC6C,oBAAqBwH,GAASzG,QAAQ/B,YACtCiB,WAAAA,EACAJ,YAA0C,MAA7B2H,GAASzG,QAAQwG,WAG1BG,mBC5I4BrI,SAACA,EAAD7B,MAAWA,EAAXwH,KAAkBA,EAAlBvH,KAAwBA,KAC1D,MAAOiK,EAAkBC,GAAuBC,WAC9C,MAEIC,EAAgBnF,SAAOlF,GAmC7B,OAjCAuF,4BAA0B,KACxB,IAAK1D,GAAY7B,IAAUqK,EAAc9G,SAAWiE,EAAKjE,QAAS,CAChE,MAAM+G,EAAUrK,EAAKsD,QAErB,GAAI+G,EAAS,CACX,MAAM/G,EAAUgH,gBAAc/C,EAAKjE,QAAS,CAC1CiH,iBAAiB,IAGbC,EAAQ,CACZ1J,EAAGuJ,EAAQtJ,KAAOuC,EAAQvC,KAC1BC,EAAGqJ,EAAQpJ,IAAMqC,EAAQrC,IACzBZ,OAAQgK,EAAQnJ,MAAQoC,EAAQpC,MAChCZ,OAAQ+J,EAAQlJ,OAASmC,EAAQnC,SAG/BqJ,EAAM1J,GAAK0J,EAAMxJ,IACnBkJ,EAAoBM,IAKtBzK,IAAUqK,EAAc9G,UAC1B8G,EAAc9G,QAAUvD,IAEzB,CAAC6B,EAAU7B,EAAOwH,EAAMvH,IAE3BuF,YAAU,KACJ0E,GACFC,EAAoB,OAErB,CAACD,IAEGA,EDqGkBQ,CAAoB,CAC3C7I,UAAWoI,GACXjK,MAAAA,EACAwH,KAAAA,EACAvH,KAAAA,IAkCF,OA/BAuF,YAAU,KACJpD,GAAa4H,GAASzG,QAAQjB,WAAaA,IAC7C0H,GAASzG,QAAQjB,SAAWA,GAG1Bd,IAAgBwI,GAASzG,QAAQ/B,cACnCwI,GAASzG,QAAQ/B,YAAcA,GAG7B7B,IAAUqK,GAASzG,QAAQ5D,QAC7BqK,GAASzG,QAAQ5D,MAAQA,IAE1B,CAACyC,EAAWE,EAAUd,EAAa7B,IAEtC6F,YAAU,KACR,GAAIuE,KAAaC,GAASzG,QAAQwG,SAChC,OAGF,GAAgB,MAAZA,IAAiD,MAA7BC,GAASzG,QAAQwG,SAEvC,YADAC,GAASzG,QAAQwG,SAAWA,IAI9B,MAAMY,EAAYC,WAAW,KAC3BZ,GAASzG,QAAQwG,SAAWA,IAC3B,IAEH,MAAO,IAAMc,aAAaF,IACzB,CAACZ,KAEG,CACL1F,OAAAA,EACA3D,YAAAA,EACAyH,WAAAA,EACA7E,KAAAA,EACArD,KAAAA,EACAD,MAAAA,EACAsC,SAAAA,EACA3C,MAAAA,EACAoJ,OAAAA,EACA3G,UAAAA,EACA4C,WAAAA,EACAsE,UAAAA,EACA9B,KAAAA,EACA7G,UAAAA,EACA6D,KAAAA,EACAwE,WAAAA,EACAO,oBAAAA,EACAN,oBAAAA,EACAI,oBAAAA,EACAG,gBAAWU,GAAAA,GAAoBJ,EAC/BrH,WAMEyH,IAEC/E,IAAoB6E,GAASzG,QAAQjB,WAAatC,EAE5C6C,EAIN+G,IAA6BkB,kBAAgB1B,KAC7C3G,OAFH,EAOIL,GAAa6H,GACRnH,MAAIC,WAAWC,SAAS,IAC1BP,EACHQ,SP7L0B,mBO0L9B,wCTjOwDxC,cAACC,YAC3DA,EACAkF,eAAgBC,EAF2C7F,MAG3DA,EAH2DJ,MAI3DA,EAJ2De,UAK3DA,KAEA,MAAMiF,WAAiBhG,EAAMc,MAAgBmF,EAE7C,IAAKD,EACH,OAAO,KAGT,GAAI5F,IAAUU,EAAa,CACzB,MAAMqK,EAAgBnL,EAAMe,GAE5B,OAAKoK,EAIE,CACLhK,EAAG,EACHE,EACEP,EAAcC,EACVoK,EAAc7J,IACd6J,EAAc3J,QACbwE,EAAe1E,IAAM0E,EAAexE,QACrC2J,EAAc7J,IAAM0E,EAAe1E,OACtCb,GAXI,KAeX,MAAMyF,EAyBR,SACEkF,EACAhL,EACAU,GAEA,MAAMqF,EAAsCiF,EAAYhL,GAClDgG,EAAuCgF,EAAYhL,EAAQ,GAC3DiG,EAAmC+E,EAAYhL,EAAQ,GAE7D,OAAK+F,EAIDrF,EAAcV,EACTgG,EACHD,EAAY7E,KAAO8E,EAAa9E,IAAM8E,EAAa5E,QACnD6E,EACAA,EAAS/E,KAAO6E,EAAY7E,IAAM6E,EAAY3E,QAC9C,EAGC6E,EACHA,EAAS/E,KAAO6E,EAAY7E,IAAM6E,EAAY3E,QAC9C4E,EACAD,EAAY7E,KAAO8E,EAAa9E,IAAM8E,EAAa5E,QACnD,EAfK,EAnCO8E,CAAWtG,EAAOI,EAAOU,GAEzC,OAAIV,EAAQU,GAAeV,GAASW,EAC3B,CACLI,EAAG,EACHE,GAAI2E,EAAexE,OAAS0E,KACzBzF,GAIHL,EAAQU,GAAeV,GAASW,EAC3B,CACLI,EAAG,EACHE,EAAG2E,EAAexE,OAAS0E,KACxBzF,GAIA,CACLU,EAAG,EACHE,EAAG,KACAZ"}
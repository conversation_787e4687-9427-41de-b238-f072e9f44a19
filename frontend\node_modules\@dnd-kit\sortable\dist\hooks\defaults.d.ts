import type { AnimateLayoutChanges, NewIndexGetter, SortableTransition } from './types';
export declare const defaultNewIndexGetter: NewIndexGetter;
export declare const defaultAnimateLayoutChanges: AnimateLayoutChanges;
export declare const defaultTransition: SortableTransition;
export declare const transitionProperty = "transform";
export declare const disabledTransition: string;
export declare const defaultAttributes: {
    roleDescription: string;
};

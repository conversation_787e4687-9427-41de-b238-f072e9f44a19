(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12266:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,75758)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\profile\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>c,yv:()=>o});var a=s(60687);s(43210);var r=s(25911),n=s(78272),i=s(13964),l=s(3589),d=s(4780);function c({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function o({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(p,{})]})})}function m({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function p({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},18144:(e,t,s)=>{Promise.resolve().then(s.bind(s,74686))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,t,s)=>{"use strict";s.d(t,{tU:()=>q,av:()=>G,j7:()=>P,Xi:()=>S});var a=s(60687),r=s(43210),n=s(70569),i=s(11273),l=s(72942),d=s(46059),c=s(14163),o=s(43),u=s(65551),x=s(96963),m="Tabs",[h,p]=(0,i.A)(m,[l.RG]),f=(0,l.RG)(),[v,g]=h(m),j=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:d,activationMode:h="automatic",...p}=e,f=(0,o.jH)(d),[g,j]=(0,u.i)({prop:r,onChange:n,defaultProp:i??"",caller:m});return(0,a.jsx)(v,{scope:s,baseId:(0,x.B)(),value:g,onValueChange:j,orientation:l,dir:f,activationMode:h,children:(0,a.jsx)(c.sG.div,{dir:f,"data-orientation":l,...p,ref:t})})});j.displayName=m;var b="TabsList",y=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...n}=e,i=g(b,s),d=f(s);return(0,a.jsx)(l.bL,{asChild:!0,...d,orientation:i.orientation,dir:i.dir,loop:r,children:(0,a.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});y.displayName=b;var N="TabsTrigger",w=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:i=!1,...d}=e,o=g(N,s),u=f(s),x=_(o.baseId,r),m=C(o.baseId,r),h=r===o.value;return(0,a.jsx)(l.q7,{asChild:!0,...u,focusable:!i,active:h,children:(0,a.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:x,...d,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;h||i||!e||o.onValueChange(r)})})})});w.displayName=N;var k="TabsContent",A=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:n,forceMount:i,children:l,...o}=e,u=g(k,s),x=_(u.baseId,n),m=C(u.baseId,n),h=n===u.value,p=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(d.C,{present:i||h,children:({present:s})=>(0,a.jsx)(c.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":x,hidden:!s,id:m,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:s&&l})})});function _(e,t){return`${e}-trigger-${t}`}function C(e,t){return`${e}-content-${t}`}A.displayName=k;var z=s(4780);function q({className:e,...t}){return(0,a.jsx)(j,{"data-slot":"tabs",className:(0,z.cn)("flex flex-col gap-2",e),...t})}function P({className:e,...t}){return(0,a.jsx)(y,{"data-slot":"tabs-list",className:(0,z.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function S({className:e,...t}){return(0,a.jsx)(w,{"data-slot":"tabs-trigger",className:(0,z.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function G({className:e,...t}){return(0,a.jsx)(A,{"data-slot":"tabs-content",className:(0,z.cn)("flex-1 outline-none",e),...t})}},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(60687);s(43210);var r=s(4780);function n({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},51361:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},53332:(e,t,s)=>{"use strict";var a=s(43210),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=a.useState,i=a.useEffect,l=a.useLayoutEffect,d=a.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!r(e,s)}catch(e){return!0}}var o="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),a=n({inst:{value:s,getSnapshot:t}}),r=a[0].inst,o=a[1];return l(function(){r.value=s,r.getSnapshot=t,c(r)&&o({inst:r})},[e,s,t]),i(function(){return c(r)&&o({inst:r}),e(function(){c(r)&&o({inst:r})})},[e]),d(s),s};t.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:o},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var a=s(60687),r=s(43210),n=s(14163),i=r.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=s(4780);function d({className:e,...t}){return(0,a.jsx)(i,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},54592:(e,t,s)=>{Promise.resolve().then(s.bind(s,75758))},57379:(e,t,s)=>{"use strict";e.exports=s(53332)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64021:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},74686:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Y});var a=s(60687),r=s(43210),n=s(63213),i=s(32884),l=s(44493),d=s(29523),c=s(89667),o=s(54300),u=s(34729),x=s(15079),m=s(96834),h=s(11273),p=s(13495),f=s(66156),v=s(14163),g=s(57379);function j(){return()=>{}}var b="Avatar",[y,N]=(0,h.A)(b),[w,k]=y(b),A=r.forwardRef((e,t)=>{let{__scopeAvatar:s,...n}=e,[i,l]=r.useState("idle");return(0,a.jsx)(w,{scope:s,imageLoadingStatus:i,onImageLoadingStatusChange:l,children:(0,a.jsx)(v.sG.span,{...n,ref:t})})});A.displayName=b;var _="AvatarImage",C=r.forwardRef((e,t)=>{let{__scopeAvatar:s,src:n,onLoadingStatusChange:i=()=>{},...l}=e,d=k(_,s),c=function(e,{referrerPolicy:t,crossOrigin:s}){let a=(0,g.useSyncExternalStore)(j,()=>!0,()=>!1),n=r.useRef(null),i=a?(n.current||(n.current=new window.Image),n.current):null,[l,d]=r.useState(()=>P(i,e));return(0,f.N)(()=>{d(P(i,e))},[i,e]),(0,f.N)(()=>{let e=e=>()=>{d(e)};if(!i)return;let a=e("loaded"),r=e("error");return i.addEventListener("load",a),i.addEventListener("error",r),t&&(i.referrerPolicy=t),"string"==typeof s&&(i.crossOrigin=s),()=>{i.removeEventListener("load",a),i.removeEventListener("error",r)}},[i,s,t]),l}(n,l),o=(0,p.c)(e=>{i(e),d.onImageLoadingStatusChange(e)});return(0,f.N)(()=>{"idle"!==c&&o(c)},[c,o]),"loaded"===c?(0,a.jsx)(v.sG.img,{...l,ref:t,src:n}):null});C.displayName=_;var z="AvatarFallback",q=r.forwardRef((e,t)=>{let{__scopeAvatar:s,delayMs:n,...i}=e,l=k(z,s),[d,c]=r.useState(void 0===n);return r.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(e)}},[n]),d&&"loaded"!==l.imageLoadingStatus?(0,a.jsx)(v.sG.span,{...i,ref:t}):null});function P(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}q.displayName=z;var S=s(4780);function G({className:e,...t}){return(0,a.jsx)(A,{"data-slot":"avatar",className:(0,S.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function T({className:e,...t}){return(0,a.jsx)(C,{"data-slot":"avatar-image",className:(0,S.cn)("aspect-square size-full",e),...t})}function D({className:e,...t}){return(0,a.jsx)(q,{"data-slot":"avatar-fallback",className:(0,S.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var E=s(29844),M=s(99891),R=s(7430),L=s(88059),V=s(27351),X=s(58869),F=s(51361),I=s(41550),$=s(48340),B=s(97992),U=s(40228),Z=s(63143),J=s(8819),W=s(11860),H=s(84027),K=s(64021),O=s(97051),Q=s(52581);function Y(){let{user:e,profile:t}=(0,n.A)(),[s,h]=(0,r.useState)(!1),[p,f]=(0,r.useState)(!1),[v,g]=(0,r.useState)({full_name:"",phone:"",address:"",bio:"",birth_date:"",gender:""}),j=async()=>{f(!0);try{await new Promise(e=>setTimeout(e,1e3)),Q.o.success("تم حفظ التغييرات بنجاح"),h(!1)}catch(e){Q.o.error("فشل في حفظ التغييرات")}finally{f(!1)}};return e&&t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(i.V,{}),(0,a.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"الملف الشخصي \uD83D\uDC64"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة معلوماتك الشخصية وإعدادات الحساب"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative mx-auto mb-4",children:[(0,a.jsxs)(G,{className:"h-24 w-24 mx-auto",children:[(0,a.jsx)(T,{src:t.avatar_url}),(0,a.jsx)(D,{className:"bg-blue-600 text-white text-xl",children:t.full_name.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]}),(0,a.jsx)(d.$,{size:"sm",variant:"outline",className:"absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0",children:(0,a.jsx)(F.A,{className:"h-4 w-4"})})]}),(0,a.jsx)(l.ZB,{className:"arabic-text",children:t.full_name}),(0,a.jsxs)(l.BT,{className:"flex items-center justify-center gap-2",children:[(e=>{switch(e){case"admin":return(0,a.jsx)(M.A,{className:"h-5 w-5 text-red-600"});case"school":return(0,a.jsx)(R.A,{className:"h-5 w-5 text-blue-600"});case"delivery":return(0,a.jsx)(L.A,{className:"h-5 w-5 text-green-600"});case"student":return(0,a.jsx)(V.A,{className:"h-5 w-5 text-purple-600"});default:return(0,a.jsx)(X.A,{className:"h-5 w-5 text-gray-600"})}})(t.role),(0,a.jsx)("span",{children:(e=>{switch(e){case"admin":return"مدير النظام";case"school":return"مدرسة";case"delivery":return"شريك توصيل";case"student":return"طالب";default:return"مستخدم"}})(t.role)})]})]}),(0,a.jsxs)(l.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(I.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.email})]}),t.phone&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)($.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.phone})]}),t.address&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(B.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.address})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(U.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["انضم في ",new Date(t.created_at).toLocaleDateString("ar-SA")]})]})]})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(E.tU,{defaultValue:"personal",className:"space-y-6",children:[(0,a.jsxs)(E.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(E.Xi,{value:"personal",className:"arabic-text",children:"المعلومات الشخصية"}),(0,a.jsx)(E.Xi,{value:"settings",className:"arabic-text",children:"الإعدادات"}),(0,a.jsx)(E.Xi,{value:"notifications",className:"arabic-text",children:"الإشعارات"})]}),(0,a.jsx)(E.av,{value:"personal",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l.ZB,{className:"arabic-text",children:"المعلومات الشخصية"}),(0,a.jsx)(l.BT,{children:"تحديث معلوماتك الشخصية والتفاصيل"})]}),s?(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(d.$,{onClick:j,size:"sm",disabled:p,children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),p?"جاري الحفظ...":"حفظ"]}),(0,a.jsxs)(d.$,{onClick:()=>{t&&g({full_name:t.full_name||"",phone:t.phone||"",address:t.address||"",bio:t.bio||"",birth_date:t.birth_date||"",gender:t.gender||""}),h(!1)},variant:"outline",size:"sm",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"إلغاء"]})]}):(0,a.jsxs)(d.$,{onClick:()=>h(!0),variant:"outline",size:"sm",children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"تحرير"]})]})}),(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"full_name",children:"الاسم الكامل"}),(0,a.jsx)(c.p,{id:"full_name",value:v.full_name,onChange:e=>g({...v,full_name:e.target.value}),disabled:!s,className:"arabic-text"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"phone",children:"رقم الهاتف"}),(0,a.jsx)(c.p,{id:"phone",value:v.phone,onChange:e=>g({...v,phone:e.target.value}),disabled:!s,placeholder:"+212 6XX-XXXXXX"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"address",children:"العنوان"}),(0,a.jsx)(c.p,{id:"address",value:v.address,onChange:e=>g({...v,address:e.target.value}),disabled:!s,className:"arabic-text",placeholder:"المدينة، المغرب"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"birth_date",children:"تاريخ الميلاد"}),(0,a.jsx)(c.p,{id:"birth_date",type:"date",value:v.birth_date,onChange:e=>g({...v,birth_date:e.target.value}),disabled:!s})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"gender",children:"الجنس"}),(0,a.jsxs)(x.l6,{value:v.gender,onValueChange:e=>g({...v,gender:e}),disabled:!s,children:[(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{placeholder:"اختر الجنس"})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"male",children:"ذكر"}),(0,a.jsx)(x.eb,{value:"female",children:"أنثى"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"bio",children:"نبذة شخصية"}),(0,a.jsx)(u.T,{id:"bio",value:v.bio,onChange:e=>g({...v,bio:e.target.value}),disabled:!s,className:"arabic-text",placeholder:"اكتب نبذة مختصرة عن نفسك...",rows:4})]})]})]})}),(0,a.jsx)(E.av,{value:"settings",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"h-5 w-5"}),"إعدادات الحساب"]}),(0,a.jsx)(l.BT,{children:"إدارة إعدادات الأمان والخصوصية"})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium arabic-text",children:"تغيير كلمة المرور"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"تحديث كلمة المرور لحسابك"})]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"تغيير"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium arabic-text",children:"المصادقة الثنائية"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"تفعيل طبقة حماية إضافية لحسابك"})]}),(0,a.jsx)(m.E,{variant:"secondary",children:"غير مفعل"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium arabic-text",children:"حذف الحساب"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"حذف حسابك وجميع البيانات المرتبطة به"})]}),(0,a.jsx)(d.$,{variant:"destructive",size:"sm",children:"حذف الحساب"})]})]})]})}),(0,a.jsx)(E.av,{value:"notifications",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,a.jsx)(O.A,{className:"h-5 w-5"}),"إعدادات الإشعارات"]}),(0,a.jsx)(l.BT,{children:"تخصيص الإشعارات التي تريد تلقيها"})]}),(0,a.jsx)(l.Wu,{className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium arabic-text",children:"إشعارات البريد الإلكتروني"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"تلقي الإشعارات عبر البريد الإلكتروني"})]}),(0,a.jsx)("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium arabic-text",children:"إشعارات الطلبات"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"تحديثات حول حالة طلباتك"})]}),(0,a.jsx)("input",{type:"checkbox",className:"toggle",defaultChecked:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium arabic-text",children:"إشعارات التسويق"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"عروض خاصة ومنتجات جديدة"})]}),(0,a.jsx)("input",{type:"checkbox",className:"toggle"})]})]})})]})})]})})]})]})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(i.V,{}),(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"جاري التحميل..."})]})})]})}},75758:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\profile\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[7719,3406,1658,5814,6312,5346,9226,4717,8995,2884],()=>s(12266));module.exports=a})();
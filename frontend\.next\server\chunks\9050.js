"use strict";exports.id=9050,exports.ids=[9050],exports.modules={26134:(e,t,a)=>{a.d(t,{G$:()=>U,Hs:()=>j,UC:()=>ea,VY:()=>er,ZL:()=>ee,bL:()=>K,bm:()=>eo,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=a(43210),r=a(70569),o=a(98599),s=a(11273),l=a(96963),i=a(65551),d=a(31355),c=a(32547),u=a(25028),f=a(46059),p=a(14163),g=a(1359),m=a(42247),x=a(63376),b=a(8730),h=a(60687),v="Dialog",[y,j]=(0,s.A)(v),[w,k]=y(v),D=e=>{let{__scopeDialog:t,children:a,open:r,defaultOpen:o,onOpenChange:s,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[f,p]=(0,i.i)({prop:r,defaultProp:o??!1,onChange:s,caller:v});return(0,h.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:a})};D.displayName=v;var C="DialogTrigger",N=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,s=k(C,a),l=(0,o.s)(t,s.triggerRef);return(0,h.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":H(s.open),...n,ref:l,onClick:(0,r.m)(e.onClick,s.onOpenToggle)})});N.displayName=C;var R="DialogPortal",[I,E]=y(R,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:a,children:r,container:o}=e,s=k(R,t);return(0,h.jsx)(I,{scope:t,forceMount:a,children:n.Children.map(r,e=>(0,h.jsx)(f.C,{present:a||s.open,children:(0,h.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};O.displayName=R;var _="DialogOverlay",P=n.forwardRef((e,t)=>{let a=E(_,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=k(_,e.__scopeDialog);return o.modal?(0,h.jsx)(f.C,{present:n||o.open,children:(0,h.jsx)(A,{...r,ref:t})}):null});P.displayName=_;var F=(0,b.TL)("DialogOverlay.RemoveScroll"),A=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=k(_,a);return(0,h.jsx)(m.A,{as:F,allowPinchZoom:!0,shards:[r.contentRef],children:(0,h.jsx)(p.sG.div,{"data-state":H(r.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),G="DialogContent",M=n.forwardRef((e,t)=>{let a=E(G,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=k(G,e.__scopeDialog);return(0,h.jsx)(f.C,{present:n||o.open,children:o.modal?(0,h.jsx)(z,{...r,ref:t}):(0,h.jsx)(L,{...r,ref:t})})});M.displayName=G;var z=n.forwardRef((e,t)=>{let a=k(G,e.__scopeDialog),s=n.useRef(null),l=(0,o.s)(t,a.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,x.Eq)(e)},[]),(0,h.jsx)(S,{...e,ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=n.forwardRef((e,t)=>{let a=k(G,e.__scopeDialog),r=n.useRef(!1),o=n.useRef(!1);return(0,h.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||a.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let n=t.target;a.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),S=n.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:l,...i}=e,u=k(G,a),f=n.useRef(null),p=(0,o.s)(t,f);return(0,g.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,h.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":H(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(X,{titleId:u.titleId}),(0,h.jsx)(Y,{contentRef:f,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=k(T,a);return(0,h.jsx)(p.sG.h2,{id:r.titleId,...n,ref:t})});B.displayName=T;var $="DialogDescription",q=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=k($,a);return(0,h.jsx)(p.sG.p,{id:r.descriptionId,...n,ref:t})});q.displayName=$;var Z="DialogClose",W=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=k(Z,a);return(0,h.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,r.m)(e.onClick,()=>o.onOpenChange(!1))})});function H(e){return e?"open":"closed"}W.displayName=Z;var J="DialogTitleWarning",[U,V]=(0,s.q)(J,{contentName:G,titleName:T,docsSlug:"dialog"}),X=({titleId:e})=>{let t=V(J),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},Y=({contentRef:e,descriptionId:t})=>{let a=V("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return n.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},K=D,Q=N,ee=O,et=P,ea=M,en=B,er=q,eo=W},27479:(e,t,a)=>{a.d(t,{d:()=>k});var n=a(60687),r=a(43210),o=a(70569),s=a(98599),l=a(11273),i=a(65551),d=a(83721),c=a(18853),u=a(14163),f="Switch",[p,g]=(0,l.A)(f),[m,x]=p(f),b=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:l,checked:d,defaultChecked:c,required:p,disabled:g,value:x="on",onCheckedChange:b,form:h,...v}=e,[w,k]=r.useState(null),D=(0,s.s)(t,e=>k(e)),C=r.useRef(!1),N=!w||h||!!w.closest("form"),[R,I]=(0,i.i)({prop:d,defaultProp:c??!1,onChange:b,caller:f});return(0,n.jsxs)(m,{scope:a,checked:R,disabled:g,children:[(0,n.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":R,"aria-required":p,"data-state":j(R),"data-disabled":g?"":void 0,disabled:g,value:x,...v,ref:D,onClick:(0,o.m)(e.onClick,e=>{I(e=>!e),N&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),N&&(0,n.jsx)(y,{control:w,bubbles:!C.current,name:l,value:x,checked:R,required:p,disabled:g,form:h,style:{transform:"translateX(-100%)"}})]})});b.displayName=f;var h="SwitchThumb",v=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,o=x(h,a);return(0,n.jsx)(u.sG.span,{"data-state":j(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});v.displayName=h;var y=r.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:o=!0,...l},i)=>{let u=r.useRef(null),f=(0,s.s)(u,i),p=(0,d.Z)(a),g=(0,c.X)(t);return r.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&t){let n=new Event("click",{bubbles:o});t.call(e,a),e.dispatchEvent(n)}},[p,a,o]),(0,n.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:f,style:{...l.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var w=a(4780);function k({className:e,...t}){return(0,n.jsx)(b,{"data-slot":"switch",className:(0,w.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,n.jsx)(v,{"data-slot":"switch-thumb",className:(0,w.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},54300:(e,t,a)=>{a.d(t,{J:()=>i});var n=a(60687),r=a(43210),o=a(14163),s=r.forwardRef((e,t)=>(0,n.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=a(4780);function i({className:e,...t}){return(0,n.jsx)(s,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},63503:(e,t,a)=>{a.d(t,{Cf:()=>u,Es:()=>p,L3:()=>g,c7:()=>f,lG:()=>l,rr:()=>m,zM:()=>i});var n=a(60687);a(43210);var r=a(26134),o=a(11860),s=a(4780);function l({...e}){return(0,n.jsx)(r.bL,{"data-slot":"dialog",...e})}function i({...e}){return(0,n.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,n.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,n.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,showCloseButton:a=!0,...l}){return(0,n.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,n.jsx)(c,{}),(0,n.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[t,a&&(0,n.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(o.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,s.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function p({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,s.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function g({className:e,...t}){return(0,n.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,s.cn)("text-lg leading-none font-semibold",e),...t})}function m({className:e,...t}){return(0,n.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}}};
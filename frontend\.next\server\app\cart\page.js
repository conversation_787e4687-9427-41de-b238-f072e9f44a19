(()=>{var e={};e.id=4005,e.ids=[4005],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16100:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var t=a(65239),r=a(48088),i=a(88170),l=a.n(i),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,70905)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24889:(e,s,a)=>{Promise.resolve().then(a.bind(a,70905))},28559:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28561:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},61337:(e,s,a)=>{Promise.resolve().then(a.bind(a,77391))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70905:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\cart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx","default")},77391:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var t=a(60687),r=a(43210),i=a(63213),l=a(32884),n=a(44493),c=a(29523),d=a(96834),x=a(89667),o=a(35950),m=a(28561),h=a(88233),p=a(62688);let u=(0,p.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var j=a(96474),g=a(37360);let b=(0,p.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var y=a(88059),f=a(99891),v=a(28559),N=a(70334);function k(){let{user:e,profile:s}=(0,i.A)(),[a,p]=(0,r.useState)([]),[k,w]=(0,r.useState)(""),[A,C]=(0,r.useState)(null),[q,P]=(0,r.useState)(!0),_=(e,s)=>{s<1||p(a=>a.map(a=>a.id===e?{...a,quantity:s}:a))},z=e=>{p(s=>s.filter(s=>s.id!==e))},E=a.reduce((e,s)=>e+s.price*s.quantity,0),G=E>500?0:25,M=A?"percentage"===A.type?E*A.discount/100:A.discount:0,D=(E-M)*.05,$=E-M+G+D;return q?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})})]}):0===a.length?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)(m.A,{className:"h-24 w-24 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"سلة التسوق فارغة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 arabic-text",children:"لم تقم بإضافة أي منتجات إلى سلة التسوق بعد"}),(0,t.jsx)(c.$,{asChild:!0,children:(0,t.jsx)("a",{href:"/catalog",className:"arabic-text",children:"تصفح المنتجات"})})]})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"سلة التسوق \uD83D\uDED2"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"راجع منتجاتك واكمل عملية الشراء"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[a.map(e=>(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0",children:(0,t.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]}),(0,t.jsx)("button",{onClick:()=>z(e.id),className:"text-red-500 hover:text-red-700 p-1",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex gap-4 mb-3",children:[e.size&&(0,t.jsxs)(d.E,{variant:"outline",className:"arabic-text",children:["المقاس: ",e.size]}),e.color&&(0,t.jsxs)(d.E,{variant:"outline",className:"arabic-text",children:["اللون: ",e.color]}),!e.inStock&&(0,t.jsx)(d.E,{variant:"destructive",className:"arabic-text",children:"غير متوفر"})]}),e.customizations&&(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 arabic-text",children:"التخصيصات:"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:Object.entries(e.customizations).map(([e,s])=>(0,t.jsxs)("span",{className:"block",children:[e,": ",s]},e))})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("button",{onClick:()=>_(e.id,e.quantity-1),className:"w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,t.jsx)(u,{className:"h-4 w-4"})}),(0,t.jsx)("span",{className:"w-8 text-center font-medium",children:e.quantity}),(0,t.jsx)("button",{onClick:()=>_(e.id,e.quantity+1),className:"w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"text-right",children:[e.originalPrice&&(0,t.jsxs)("p",{className:"text-sm text-gray-500 line-through",children:[e.originalPrice," درهم"]}),(0,t.jsxs)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:[(e.price*e.quantity).toFixed(2)," درهم"]})]})]})]})]})})},e.id)),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(g.A,{className:"h-5 w-5"}),"كود الخصم"]})}),(0,t.jsx)(n.Wu,{children:A?(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(b,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"font-medium text-green-800 dark:text-green-200 arabic-text",children:A.code}),(0,t.jsx)(d.E,{variant:"secondary",children:"percentage"===A.type?`${A.discount}%`:`${A.discount} درهم`})]}),(0,t.jsx)("button",{onClick:()=>{C(null)},className:"text-red-500 hover:text-red-700",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}):(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.p,{placeholder:"أدخل كود الخصم",value:k,onChange:e=>w(e.target.value),className:"arabic-text"}),(0,t.jsx)(c.$,{variant:"outline",onClick:()=>{let e=[{code:"GRAD2024",discount:15,type:"percentage",minAmount:200},{code:"WELCOME50",discount:50,type:"fixed",minAmount:100}].find(e=>e.code.toLowerCase()===k.toLowerCase());e&&E>=(e.minAmount||0)?(C(e),w("")):alert("كود الخصم غير صالح أو لا يحقق الحد الأدنى للمبلغ")},disabled:!k.trim(),children:"تطبيق"})]})})]})]}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)(n.Zp,{className:"sticky top-24",children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"ملخص الطلب"})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,t.jsxs)("span",{children:[E.toFixed(2)," درهم"]})]}),M>0&&(0,t.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الخصم:"}),(0,t.jsxs)("span",{children:["-",M.toFixed(2)," درهم"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,t.jsx)("span",{children:0===G?(0,t.jsx)("span",{className:"text-green-600 arabic-text",children:"مجاني"}):`${G} درهم`})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الضريبة (5%):"}),(0,t.jsxs)("span",{children:[D.toFixed(2)," درهم"]})]}),(0,t.jsx)(o.w,{}),(0,t.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{children:[$.toFixed(2)," درهم"]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"معلومات الشحن"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:0===G?"شحن مجاني للطلبات أكثر من 500 درهم":"التوصيل خلال 2-3 أيام عمل"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"arabic-text",children:"دفع آمن ومضمون"})]}),(0,t.jsx)(c.$,{className:"w-full",size:"lg",asChild:!0,children:(0,t.jsxs)("a",{href:"/checkout",className:"arabic-text",children:["متابعة للدفع",(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"})]})}),(0,t.jsx)(c.$,{variant:"outline",className:"w-full arabic-text",asChild:!0,children:(0,t.jsxs)("a",{href:"/catalog",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 ml-2"}),"متابعة التسوق"]})})]})]})})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},96474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[7719,3406,1658,5814,6312,5346,4717,8995,2884],()=>a(16100));module.exports=t})();
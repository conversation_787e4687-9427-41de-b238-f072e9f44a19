exports.id=4717,exports.ids=[4717],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(49384),o=r(82348);function n(...e){return(0,o.QP)((0,s.$)(e))}},8827:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call UserRole() from the server but UserRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx","UserRole");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>a});var s=r(60687);r(43210);var o=r(8730),n=r(24224),i=r(4780);let a=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let d=n?o.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(a({variant:t,size:r,className:e})),...l})}},40715:(e,t,r)=>{"use strict";r.d(t,{LiveChat:()=>g});var s=r(60687),o=r(43210),n=r(63213),i=r(44493),a=r(29523),l=r(89667),d=r(42692),c=r(48730),u=r(5336),m=r(58887),h=r(79351),f=r(49153),v=r(11860),x=r(3876),p=r(60020),b=r(27900);function g(){let{user:e,profile:t}=(0,n.A)(),[r,g]=(0,o.useState)(!1),[N,w]=(0,o.useState)(!1),[j,C]=(0,o.useState)([]),[y,P]=(0,o.useState)(""),[k,A]=(0,o.useState)(!1),[S,T]=(0,o.useState)(null),[D,I]=(0,o.useState)(!1),R=(0,o.useRef)(null),U=e=>{let t={...e,id:Date.now().toString(),timestamp:new Date().toISOString(),status:e.isFromUser?"sending":"delivered"};C(e=>[...e,t]),e.isFromUser&&setTimeout(()=>{C(e=>e.map(e=>e.id===t.id?{...e,status:"delivered"}:e))},1e3)},E=()=>{y.trim()&&(U({content:y,isFromUser:!0}),P(""),I(!0),setTimeout(()=>{I(!1);let e=["شكراً لك على تواصلك معنا. سأقوم بالتحقق من هذا الأمر.","فهمت طلبك. دعني أساعدك في حل هذه المشكلة.","هذا سؤال ممتاز. سأحتاج لبعض التفاصيل الإضافية.","سأقوم بتحويل طلبك للقسم المختص وسنتواصل معك قريباً.","هل يمكنك تزويدي برقم الطلب لأتمكن من مساعدتك بشكل أفضل؟"];U({content:e[Math.floor(Math.random()*e.length)],isFromUser:!1})},2e3))},$=e=>{switch(e){case"sending":return(0,s.jsx)(c.A,{className:"h-3 w-3 text-gray-400"});case"sent":return(0,s.jsx)(u.A,{className:"h-3 w-3 text-gray-400"});case"delivered":return(0,s.jsx)(u.A,{className:"h-3 w-3 text-blue-500"});case"read":return(0,s.jsx)(u.A,{className:"h-3 w-3 text-green-500"});default:return null}};return r?(0,s.jsxs)(i.Zp,{className:`fixed bottom-6 right-6 z-50 shadow-xl transition-all duration-300 ${N?"w-80 h-16":"w-80 h-96"}`,children:[(0,s.jsx)(i.aR,{className:"p-4 bg-blue-600 text-white rounded-t-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-white rounded-full flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})}),k&&(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-sm arabic-text",children:k?"الدردشة المباشرة":"جاري الاتصال..."}),S&&(0,s.jsxs)("p",{className:"text-xs opacity-90 arabic-text",children:[S.name," - ","online"===S.status?"متاح":"غير متاح"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(a.$,{variant:"ghost",size:"sm",onClick:()=>w(!N),className:"h-8 w-8 p-0 text-white hover:bg-white/20",children:N?(0,s.jsx)(h.A,{className:"h-4 w-4"}):(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(a.$,{variant:"ghost",size:"sm",onClick:()=>g(!1),className:"h-8 w-8 p-0 text-white hover:bg-white/20",children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]})]})}),!N&&(0,s.jsxs)(i.Wu,{className:"p-0 flex flex-col h-80",children:[(0,s.jsx)(d.F,{className:"flex-1 p-4",children:k?(0,s.jsxs)("div",{className:"space-y-4",children:[j.map(e=>(0,s.jsx)("div",{className:`flex ${e.isFromUser?"justify-end":"justify-start"}`,children:(0,s.jsxs)("div",{className:`max-w-[80%] ${e.isFromUser?"bg-blue-600 text-white rounded-l-lg rounded-tr-lg":"bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg"} p-3`,children:[(0,s.jsx)("p",{className:"text-sm arabic-text",children:e.content}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,s.jsx)("span",{className:"text-xs opacity-70",children:new Date(e.timestamp).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit"})}),e.isFromUser&&$(e.status)]})]})},e.id)),D&&(0,s.jsx)("div",{className:"flex justify-start",children:(0,s.jsx)("div",{className:"bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,s.jsx)("span",{className:"text-xs text-gray-500 mr-2 arabic-text",children:"يكتب..."})]})})}),(0,s.jsx)("div",{ref:R})]}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"جاري الاتصال بفريق الدعم..."})]})})}),k&&(0,s.jsxs)("div",{className:"p-4 border-t",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(a.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.p,{value:y,onChange:e=>P(e.target.value),placeholder:"اكتب رسالتك...",className:"flex-1 arabic-text",onKeyPress:e=>{"Enter"===e.key&&E()}}),(0,s.jsx)(a.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)(a.$,{onClick:E,disabled:!y.trim(),size:"sm",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500",children:[(0,s.jsx)("span",{className:"arabic-text",children:"اضغط Enter للإرسال"}),S&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsxs)("span",{className:"arabic-text",children:[S.name," متاح"]})]})]})]})]})]}):(0,s.jsx)(a.$,{onClick:()=>g(!0),className:"fixed bottom-6 right-6 z-50 rounded-full w-14 h-14 shadow-lg",size:"lg",children:(0,s.jsx)(m.A,{className:"h-6 w-6"})})}},42629:(e,t,r)=>{"use strict";r.d(t,{LiveChat:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call LiveChat() from the server but LiveChat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx","LiveChat");(0,s.registerClientReference)(function(){throw Error("Attempted to call ChatButton() from the server but ChatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx","ChatButton")},42692:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(60687);r(43210);var o=r(68123),n=r(4780);function i({className:e,children:t,...r}){return(0,s.jsxs)(o.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",e),...r,children:[(0,s.jsx)(o.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:t}),(0,s.jsx)(a,{}),(0,s.jsx)(o.OK,{})]})}function a({className:e,orientation:t="vertical",...r}){return(0,s.jsx)(o.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...r,children:(0,s.jsx)(o.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>a,Zp:()=>n,aR:()=>i});var s=r(60687);r(43210);var o=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}},46952:(e,t,r)=>{"use strict";r.d(t,{By:()=>d,E$:()=>u,NotificationProvider:()=>a,bQ:()=>c,lO:()=>l});var s=r(60687),o=r(43210),n=r(63213);let i=(0,o.createContext)(void 0);function a({children:e}){let{user:t}=(0,n.A)(),[r,a]=(0,o.useState)([]),l=()=>r.filter(e=>!e.isRead),d=l().length;return(0,s.jsx)(i.Provider,{value:{notifications:r,unreadCount:d,addNotification:e=>{let r={...e,id:Date.now().toString(),createdAt:new Date().toISOString(),userId:t?.id||"",isRead:!1};a(e=>[r,...e]),"granted"===Notification.permission&&new Notification(r.title,{body:r.message,icon:"/favicon.ico",tag:r.id})},markAsRead:e=>{a(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))},markAllAsRead:()=>{a(e=>e.map(e=>({...e,isRead:!0})))},removeNotification:e=>{a(t=>t.filter(t=>t.id!==e))},clearAll:()=>{a([])},getNotificationsByType:e=>r.filter(t=>t.type===e),getUnreadNotifications:l},children:e})}function l(){let e=(0,o.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}function d(e){switch(e){case"order_confirmed":case"order_shipped":case"order_delivered":return"\uD83D\uDCE6";case"payment_received":return"\uD83D\uDCB3";case"payment_failed":return"❌";case"promotion":return"\uD83C\uDF89";case"reminder":return"⏰";case"system":return"⚙️";case"message":return"\uD83D\uDCAC";case"review_request":return"⭐";default:return"\uD83D\uDD14"}}function c(e){switch(e){case"urgent":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}}function u(e){let t=new Date,r=new Date(e),s=Math.floor((t.getTime()-r.getTime())/6e4);if(s<1)return"الآن";if(s<60)return`منذ ${s} دقيقة`;if(s<1440){let e=Math.floor(s/60);return`منذ ${e} ساعة`}{let e=Math.floor(s/1440);return`منذ ${e} يوم`}}},48698:(e,t,r)=>{Promise.resolve().then(r.bind(r,52581)),Promise.resolve().then(r.bind(r,40715)),Promise.resolve().then(r.bind(r,96871)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,46952))},61135:()=>{},61971:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>a,g:()=>n});var s=r(60687),o=r(43210),n=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});let i=(0,o.createContext)(void 0);function a({children:e}){let[t,r]=(0,o.useState)(null),[n,a]=(0,o.useState)(null),[l,d]=(0,o.useState)(!1),c=async(e,t,r)=>(console.log("Sign up:",e,r),{data:{user:{id:"1",email:e}},error:null}),u=async(e,t)=>{console.log("Sign in:",e);let s={id:"1",email:e},o="student";e.includes("admin")?o="admin":e.includes("school")?o="school":e.includes("delivery")&&(o="delivery");let n={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:o,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return r(s),a(n),localStorage.setItem("mockUser",JSON.stringify(s)),localStorage.setItem("mockProfile",JSON.stringify(n)),setTimeout(()=>{"admin"===o?window.location.href="/dashboard/admin":"school"===o?window.location.href="/dashboard/school":"delivery"===o?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:s},error:null}},m=async()=>(r(null),a(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),{error:null}),h=async e=>{if(!t)return{data:null,error:"No user logged in"};let r={...n,...e};return a(r),{data:r,error:null}};return(0,s.jsx)(i.Provider,{value:{user:t,profile:n,loading:l,signUp:c,signIn:u,signOut:m,updateProfile:h,hasRole:e=>{if(!n)return!1;let t={admin:4,school:3,delivery:2,student:1};return t[n.role]>=t[e]}},children:e})}function l(){let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},81326:(e,t,r)=>{"use strict";r.d(t,{NotificationProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","NotificationProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","useNotifications"),(0,s.registerClientReference)(function(){throw Error("Attempted to call getNotificationIcon() from the server but getNotificationIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","getNotificationIcon"),(0,s.registerClientReference)(function(){throw Error("Attempted to call getNotificationColor() from the server but getNotificationColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","getNotificationColor"),(0,s.registerClientReference)(function(){throw Error("Attempted to call formatNotificationTime() from the server but formatNotificationTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","formatNotificationTime")},83701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-provider.tsx","ThemeProvider")},85490:(e,t,r)=>{Promise.resolve().then(r.bind(r,6931)),Promise.resolve().then(r.bind(r,42629)),Promise.resolve().then(r.bind(r,83701)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,81326))},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var o=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>h});var s=r(37413),o=r(92676),n=r.n(o),i=r(68726),a=r.n(i);r(61135);var l=r(83701),d=r(29131),c=r(81326),u=r(42629),m=r(6931);let h={title:"Graduation Toqs - منصة أزياء التخرج المغربية",description:"أول منصة مغربية ذكية لتأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي"};function f({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${n().variable} ${a().variable} antialiased font-cairo`,children:(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,s.jsx)(d.AuthProvider,{children:(0,s.jsxs)(c.NotificationProvider,{children:[e,(0,s.jsx)(u.LiveChat,{}),(0,s.jsx)(m.Toaster,{position:"top-right",dir:"rtl",richColors:!0,closeButton:!0})]})})})})})}},96871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var s=r(60687);r(43210);var o=r(10218);function n({children:e,...t}){return(0,s.jsx)(o.N,{...t,children:e})}}};